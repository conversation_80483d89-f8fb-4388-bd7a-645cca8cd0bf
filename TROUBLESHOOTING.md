# 臭屁屁的血糖记录APP故障排除指南

## 🔧 常见编译问题及解决方案

### 1. Java环境问题
**问题**: `JAVA_HOME is not set and no 'java' command could be found`

**解决方案**:
1. 确保已安装Java JDK 11或更高版本
2. 在Android Studio中打开项目，它会自动配置Java环境
3. 或者手动设置JAVA_HOME环境变量

### 2. 依赖同步问题
**问题**: Gradle同步失败或依赖无法解析

**解决方案**:
1. 在Android Studio中点击 "Sync Project with Gradle Files"
2. 清理项目: Build → Clean Project
3. 重新构建: Build → Rebuild Project

### 3. Hilt编译问题
**问题**: Hilt相关的编译错误

**解决方案**:
1. 确保在Application类上添加了`@HiltAndroidApp`注解
2. 确保在Activity上添加了`@AndroidEntryPoint`注解
3. 检查kapt插件是否正确配置

### 4. Room数据库问题
**问题**: Room相关的编译错误

**解决方案**:
1. 确保实体类正确标注了`@Entity`
2. 确保DAO接口正确标注了`@Dao`
3. 确保数据库类正确标注了`@Database`

## 🚀 推荐的构建步骤

### 在Android Studio中:
1. **打开项目**: File → Open → 选择项目文件夹
2. **等待同步**: 等待Gradle自动同步完成
3. **清理构建**: Build → Clean Project
4. **重新构建**: Build → Rebuild Project
5. **运行应用**: Run → Run 'app'

### 命令行构建 (需要Java环境):
```bash
# Windows
gradlew.bat clean
gradlew.bat build

# Linux/Mac
./gradlew clean
./gradlew build
```

## 📱 最低系统要求

- **开发环境**: Android Studio Hedgehog (2023.1.1) 或更新版本
- **Java版本**: JDK 11 或更高版本
- **Android SDK**: API Level 35 (Android 15)
- **最低支持**: API Level 24 (Android 7.0)

## 🔍 调试技巧

### 1. 检查依赖版本兼容性
确保所有依赖版本相互兼容，特别是:
- Compose BOM版本
- Kotlin版本
- AGP版本

### 2. 查看详细错误信息
在Android Studio的Build窗口中查看完整的错误堆栈信息

### 3. 增量构建问题
如果遇到奇怪的编译错误，尝试:
1. Build → Clean Project
2. File → Invalidate Caches and Restart

## 📋 项目检查清单

### 必需文件检查:
- [ ] `app/build.gradle.kts` - 应用级构建配置
- [ ] `gradle/libs.versions.toml` - 版本目录
- [ ] `app/src/main/AndroidManifest.xml` - 应用清单
- [ ] `app/src/main/java/com/example/myapp/BloodSugarApplication.kt` - 应用类
- [ ] `app/src/main/java/com/example/myapp/MainActivity.kt` - 主Activity

### 关键注解检查:
- [ ] `@HiltAndroidApp` 在Application类上
- [ ] `@AndroidEntryPoint` 在MainActivity上
- [ ] `@Entity` 在BloodSugar类上
- [ ] `@Dao` 在BloodSugarDao接口上
- [ ] `@Database` 在BloodSugarDatabase类上

## 🆘 如果仍然有问题

1. **检查Android Studio版本**: 确保使用最新稳定版本
2. **检查SDK工具**: 确保Android SDK和构建工具是最新版本
3. **重新创建项目**: 如果问题持续，考虑重新创建项目并逐步添加代码
4. **查看官方文档**: 参考Android官方文档和Jetpack Compose文档

## 📞 获取帮助

如果遇到特定错误，请提供:
1. 完整的错误信息
2. Android Studio版本
3. Gradle版本
4. 操作系统信息

这样可以更好地诊断和解决问题。
