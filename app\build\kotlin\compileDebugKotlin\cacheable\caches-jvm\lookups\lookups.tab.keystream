  Activity android.app  Application android.app  
HomeScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  
MyappTheme android.app.Activity  Surface android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  
HomeScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  
MyappTheme android.content.Context  Surface android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  
setContent android.content.Context  
HomeScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  
MyappTheme android.content.ContextWrapper  Surface android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
HomeScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  
MyappTheme  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
HomeScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  
MyappTheme #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  Canvas androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
BloodSugar "androidx.compose.foundation.layout  BloodSugarChart "androidx.compose.foundation.layout  BloodSugarItem "androidx.compose.foundation.layout  BloodSugarViewModel "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  MeasurementType "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Path "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  SimpleDateFormat "androidx.compose.foundation.layout  SimpleLineChart "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  
StatisticItem "androidx.compose.foundation.layout  
Statistics "androidx.compose.foundation.layout  StatisticsCard "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Stroke "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getBloodSugarColor "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  maxOf "androidx.compose.foundation.layout  minOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  sortedBy "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  
toFloatOrNull "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  BloodSugarChart .androidx.compose.foundation.layout.ColumnScope  BloodSugarItem .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Locale .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  MeasurementType .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SimpleDateFormat .androidx.compose.foundation.layout.ColumnScope  SimpleLineChart .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  
StatisticItem .androidx.compose.foundation.layout.ColumnScope  StatisticsCard .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  getBloodSugarColor .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Locale +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  SimpleDateFormat +androidx.compose.foundation.layout.RowScope  
StatisticItem +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  getBloodSugarColor +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  BloodSugarItem .androidx.compose.foundation.lazy.LazyItemScope  BloodSugarItem .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
BloodSugar androidx.compose.material3  BloodSugarChart androidx.compose.material3  BloodSugarItem androidx.compose.material3  BloodSugarViewModel androidx.compose.material3  Box androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Date androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  Float androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  List androidx.compose.material3  Locale androidx.compose.material3  
MaterialTheme androidx.compose.material3  MeasurementType androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  Path androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  SimpleDateFormat androidx.compose.material3  SimpleLineChart androidx.compose.material3  Spacer androidx.compose.material3  
StatisticItem androidx.compose.material3  
Statistics androidx.compose.material3  StatisticsCard androidx.compose.material3  String androidx.compose.material3  Stroke androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TrailingIcon androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  androidx androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  forEachIndexed androidx.compose.material3  format androidx.compose.material3  getBloodSugarColor androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotEmpty androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  maxOf androidx.compose.material3  minOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  sortedBy androidx.compose.material3  spacedBy androidx.compose.material3  take androidx.compose.material3  
toFloatOrNull androidx.compose.material3  topAppBarColors androidx.compose.material3  weight androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  MeasurementType 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
BloodSugar androidx.compose.runtime  BloodSugarChart androidx.compose.runtime  BloodSugarItem androidx.compose.runtime  BloodSugarViewModel androidx.compose.runtime  Box androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Date androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  Float androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  Locale androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  MeasurementType androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  SimpleDateFormat androidx.compose.runtime  State androidx.compose.runtime  StatisticsCard androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TrailingIcon androidx.compose.runtime  Unit androidx.compose.runtime  
cardElevation androidx.compose.runtime  collectAsState androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getBloodSugarColor androidx.compose.runtime  getValue androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  take androidx.compose.runtime  
toFloatOrNull androidx.compose.runtime  topAppBarColors androidx.compose.runtime  weight androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  Blue "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  lineTo !androidx.compose.ui.graphics.Path  moveTo !androidx.compose.ui.graphics.Path  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  drawPath 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Decimal +androidx.compose.ui.text.input.KeyboardType  Decimal 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
HomeScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  
MyappTheme #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  
BloodSugar androidx.lifecycle.ViewModel  BloodSugarUiState androidx.lifecycle.ViewModel  Calendar androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  
Statistics androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  loadStatistics androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  update androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  
BloodSugar 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Date 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Float 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  Int 
androidx.room  List 
androidx.room  Long 
androidx.room  MeasurementType 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  databaseBuilder androidx.room.Room  
BloodSugarDao androidx.room.RoomDatabase  BloodSugarDatabase androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  BloodSugarDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  AndroidEntryPoint com.example.myapp  Application com.example.myapp  BloodSugarApplication com.example.myapp  Bundle com.example.myapp  ComponentActivity com.example.myapp  HiltAndroidApp com.example.myapp  
HomeScreen com.example.myapp  MainActivity com.example.myapp  
MaterialTheme com.example.myapp  Modifier com.example.myapp  
MyappTheme com.example.myapp  Surface com.example.myapp  fillMaxSize com.example.myapp  
HomeScreen com.example.myapp.MainActivity  
MaterialTheme com.example.myapp.MainActivity  Modifier com.example.myapp.MainActivity  
MyappTheme com.example.myapp.MainActivity  Surface com.example.myapp.MainActivity  enableEdgeToEdge com.example.myapp.MainActivity  fillMaxSize com.example.myapp.MainActivity  
setContent com.example.myapp.MainActivity  
Converters  com.example.myapp.data.converter  Date  com.example.myapp.data.converter  Long  com.example.myapp.data.converter  MeasurementType  com.example.myapp.data.converter  String  com.example.myapp.data.converter  
TypeConverter  com.example.myapp.data.converter  let  com.example.myapp.data.converter  Date +com.example.myapp.data.converter.Converters  MeasurementType +com.example.myapp.data.converter.Converters  let +com.example.myapp.data.converter.Converters  
BloodSugar com.example.myapp.data.dao  
BloodSugarDao com.example.myapp.data.dao  Dao com.example.myapp.data.dao  Date com.example.myapp.data.dao  Delete com.example.myapp.data.dao  Float com.example.myapp.data.dao  Flow com.example.myapp.data.dao  Insert com.example.myapp.data.dao  Int com.example.myapp.data.dao  List com.example.myapp.data.dao  Long com.example.myapp.data.dao  MeasurementType com.example.myapp.data.dao  Query com.example.myapp.data.dao  Update com.example.myapp.data.dao  deleteBloodSugar (com.example.myapp.data.dao.BloodSugarDao  deleteBloodSugarById (com.example.myapp.data.dao.BloodSugarDao  getAllBloodSugars (com.example.myapp.data.dao.BloodSugarDao  getAverageBloodSugar (com.example.myapp.data.dao.BloodSugarDao  getBloodSugarById (com.example.myapp.data.dao.BloodSugarDao  getBloodSugarsByDateRange (com.example.myapp.data.dao.BloodSugarDao  getBloodSugarsByType (com.example.myapp.data.dao.BloodSugarDao  getMaxBloodSugar (com.example.myapp.data.dao.BloodSugarDao  getMinBloodSugar (com.example.myapp.data.dao.BloodSugarDao  getRecordCount (com.example.myapp.data.dao.BloodSugarDao  insertBloodSugar (com.example.myapp.data.dao.BloodSugarDao  updateBloodSugar (com.example.myapp.data.dao.BloodSugarDao  
BloodSugar com.example.myapp.data.database  
BloodSugarDao com.example.myapp.data.database  BloodSugarDatabase com.example.myapp.data.database  Context com.example.myapp.data.database  
Converters com.example.myapp.data.database  Database com.example.myapp.data.database  Room com.example.myapp.data.database  RoomDatabase com.example.myapp.data.database  TypeConverters com.example.myapp.data.database  Volatile com.example.myapp.data.database  databaseBuilder com.example.myapp.data.database  java com.example.myapp.data.database  synchronized com.example.myapp.data.database  
BloodSugarDao 2com.example.myapp.data.database.BloodSugarDatabase  BloodSugarDatabase 2com.example.myapp.data.database.BloodSugarDatabase  	Companion 2com.example.myapp.data.database.BloodSugarDatabase  Context 2com.example.myapp.data.database.BloodSugarDatabase  INSTANCE 2com.example.myapp.data.database.BloodSugarDatabase  Room 2com.example.myapp.data.database.BloodSugarDatabase  Volatile 2com.example.myapp.data.database.BloodSugarDatabase  
bloodSugarDao 2com.example.myapp.data.database.BloodSugarDatabase  databaseBuilder 2com.example.myapp.data.database.BloodSugarDatabase  java 2com.example.myapp.data.database.BloodSugarDatabase  synchronized 2com.example.myapp.data.database.BloodSugarDatabase  BloodSugarDatabase <com.example.myapp.data.database.BloodSugarDatabase.Companion  INSTANCE <com.example.myapp.data.database.BloodSugarDatabase.Companion  Room <com.example.myapp.data.database.BloodSugarDatabase.Companion  databaseBuilder <com.example.myapp.data.database.BloodSugarDatabase.Companion  java <com.example.myapp.data.database.BloodSugarDatabase.Companion  synchronized <com.example.myapp.data.database.BloodSugarDatabase.Companion  
BloodSugar com.example.myapp.data.entity  Date com.example.myapp.data.entity  Entity com.example.myapp.data.entity  Float com.example.myapp.data.entity  Long com.example.myapp.data.entity  MeasurementType com.example.myapp.data.entity  
PrimaryKey com.example.myapp.data.entity  String com.example.myapp.data.entity  copy (com.example.myapp.data.entity.BloodSugar  dateTime (com.example.myapp.data.entity.BloodSugar  let (com.example.myapp.data.entity.BloodSugar  measurementType (com.example.myapp.data.entity.BloodSugar  note (com.example.myapp.data.entity.BloodSugar  value (com.example.myapp.data.entity.BloodSugar  RANDOM -com.example.myapp.data.entity.MeasurementType  displayName -com.example.myapp.data.entity.MeasurementType  entries -com.example.myapp.data.entity.MeasurementType  name -com.example.myapp.data.entity.MeasurementType  valueOf -com.example.myapp.data.entity.MeasurementType  
BloodSugar !com.example.myapp.data.repository  
BloodSugarDao !com.example.myapp.data.repository  BloodSugarRepository !com.example.myapp.data.repository  Date !com.example.myapp.data.repository  Float !com.example.myapp.data.repository  Flow !com.example.myapp.data.repository  Inject !com.example.myapp.data.repository  Int !com.example.myapp.data.repository  List !com.example.myapp.data.repository  Long !com.example.myapp.data.repository  MeasurementType !com.example.myapp.data.repository  	Singleton !com.example.myapp.data.repository  
bloodSugarDao 6com.example.myapp.data.repository.BloodSugarRepository  deleteBloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  getAllBloodSugars 6com.example.myapp.data.repository.BloodSugarRepository  getAverageBloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  getMaxBloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  getMinBloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  getRecordCount 6com.example.myapp.data.repository.BloodSugarRepository  insertBloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  updateBloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  ApplicationContext com.example.myapp.di  
BloodSugarDao com.example.myapp.di  BloodSugarDatabase com.example.myapp.di  Context com.example.myapp.di  DatabaseModule com.example.myapp.di  	InstallIn com.example.myapp.di  Module com.example.myapp.di  Provides com.example.myapp.di  Room com.example.myapp.di  	Singleton com.example.myapp.di  SingletonComponent com.example.myapp.di  databaseBuilder com.example.myapp.di  java com.example.myapp.di  BloodSugarDatabase #com.example.myapp.di.DatabaseModule  Room #com.example.myapp.di.DatabaseModule  databaseBuilder #com.example.myapp.di.DatabaseModule  java #com.example.myapp.di.DatabaseModule  AddBloodSugarDialog com.example.myapp.ui.components  AlertDialog com.example.myapp.ui.components  	Alignment com.example.myapp.ui.components  Arrangement com.example.myapp.ui.components  
BloodSugar com.example.myapp.ui.components  BloodSugarChart com.example.myapp.ui.components  BloodSugarItem com.example.myapp.ui.components  Box com.example.myapp.ui.components  Card com.example.myapp.ui.components  CardDefaults com.example.myapp.ui.components  Color com.example.myapp.ui.components  Column com.example.myapp.ui.components  
Composable com.example.myapp.ui.components  Date com.example.myapp.ui.components  DropdownMenuItem com.example.myapp.ui.components  EditBloodSugarDialog com.example.myapp.ui.components  ExperimentalMaterial3Api com.example.myapp.ui.components  ExposedDropdownMenuBox com.example.myapp.ui.components  ExposedDropdownMenuDefaults com.example.myapp.ui.components  Float com.example.myapp.ui.components  
FontWeight com.example.myapp.ui.components  Icon com.example.myapp.ui.components  
IconButton com.example.myapp.ui.components  Icons com.example.myapp.ui.components  KeyboardOptions com.example.myapp.ui.components  KeyboardType com.example.myapp.ui.components  List com.example.myapp.ui.components  Locale com.example.myapp.ui.components  
MaterialTheme com.example.myapp.ui.components  MeasurementType com.example.myapp.ui.components  Modifier com.example.myapp.ui.components  Offset com.example.myapp.ui.components  OptIn com.example.myapp.ui.components  OutlinedTextField com.example.myapp.ui.components  Path com.example.myapp.ui.components  Row com.example.myapp.ui.components  SimpleDateFormat com.example.myapp.ui.components  SimpleLineChart com.example.myapp.ui.components  Spacer com.example.myapp.ui.components  
StatisticItem com.example.myapp.ui.components  
Statistics com.example.myapp.ui.components  StatisticsCard com.example.myapp.ui.components  String com.example.myapp.ui.components  Stroke com.example.myapp.ui.components  Text com.example.myapp.ui.components  
TextButton com.example.myapp.ui.components  TrailingIcon com.example.myapp.ui.components  Unit com.example.myapp.ui.components  androidx com.example.myapp.ui.components  
cardColors com.example.myapp.ui.components  
cardElevation com.example.myapp.ui.components  fillMaxWidth com.example.myapp.ui.components  forEach com.example.myapp.ui.components  forEachIndexed com.example.myapp.ui.components  format com.example.myapp.ui.components  getBloodSugarColor com.example.myapp.ui.components  getValue com.example.myapp.ui.components  height com.example.myapp.ui.components  
isNotEmpty com.example.myapp.ui.components  maxOf com.example.myapp.ui.components  minOf com.example.myapp.ui.components  mutableStateOf com.example.myapp.ui.components  padding com.example.myapp.ui.components  provideDelegate com.example.myapp.ui.components  remember com.example.myapp.ui.components  setValue com.example.myapp.ui.components  sortedBy com.example.myapp.ui.components  spacedBy com.example.myapp.ui.components  
toFloatOrNull com.example.myapp.ui.components  weight com.example.myapp.ui.components  	Alignment com.example.myapp.ui.screen  Arrangement com.example.myapp.ui.screen  
BloodSugar com.example.myapp.ui.screen  BloodSugarChart com.example.myapp.ui.screen  BloodSugarItem com.example.myapp.ui.screen  BloodSugarViewModel com.example.myapp.ui.screen  Box com.example.myapp.ui.screen  CircularProgressIndicator com.example.myapp.ui.screen  Column com.example.myapp.ui.screen  
Composable com.example.myapp.ui.screen  ExperimentalMaterial3Api com.example.myapp.ui.screen  FloatingActionButton com.example.myapp.ui.screen  
HomeScreen com.example.myapp.ui.screen  Icon com.example.myapp.ui.screen  Icons com.example.myapp.ui.screen  LaunchedEffect com.example.myapp.ui.screen  
LazyColumn com.example.myapp.ui.screen  
MaterialTheme com.example.myapp.ui.screen  Modifier com.example.myapp.ui.screen  OptIn com.example.myapp.ui.screen  Scaffold com.example.myapp.ui.screen  StatisticsCard com.example.myapp.ui.screen  Text com.example.myapp.ui.screen  	TextAlign com.example.myapp.ui.screen  	TopAppBar com.example.myapp.ui.screen  TopAppBarDefaults com.example.myapp.ui.screen  fillMaxSize com.example.myapp.ui.screen  getValue com.example.myapp.ui.screen  let com.example.myapp.ui.screen  mutableStateOf com.example.myapp.ui.screen  padding com.example.myapp.ui.screen  provideDelegate com.example.myapp.ui.screen  remember com.example.myapp.ui.screen  setValue com.example.myapp.ui.screen  spacedBy com.example.myapp.ui.screen  take com.example.myapp.ui.screen  topAppBarColors com.example.myapp.ui.screen  Boolean com.example.myapp.ui.theme  Build com.example.myapp.ui.theme  
Composable com.example.myapp.ui.theme  DarkColorScheme com.example.myapp.ui.theme  
FontFamily com.example.myapp.ui.theme  
FontWeight com.example.myapp.ui.theme  LightColorScheme com.example.myapp.ui.theme  
MyappTheme com.example.myapp.ui.theme  Pink40 com.example.myapp.ui.theme  Pink80 com.example.myapp.ui.theme  Purple40 com.example.myapp.ui.theme  Purple80 com.example.myapp.ui.theme  PurpleGrey40 com.example.myapp.ui.theme  PurpleGrey80 com.example.myapp.ui.theme  
Typography com.example.myapp.ui.theme  Unit com.example.myapp.ui.theme  
BloodSugar com.example.myapp.ui.viewmodel  BloodSugarRepository com.example.myapp.ui.viewmodel  BloodSugarUiState com.example.myapp.ui.viewmodel  BloodSugarViewModel com.example.myapp.ui.viewmodel  Boolean com.example.myapp.ui.viewmodel  Calendar com.example.myapp.ui.viewmodel  Date com.example.myapp.ui.viewmodel  	Exception com.example.myapp.ui.viewmodel  Float com.example.myapp.ui.viewmodel  
HiltViewModel com.example.myapp.ui.viewmodel  Inject com.example.myapp.ui.viewmodel  Int com.example.myapp.ui.viewmodel  List com.example.myapp.ui.viewmodel  MeasurementType com.example.myapp.ui.viewmodel  MutableStateFlow com.example.myapp.ui.viewmodel  	StateFlow com.example.myapp.ui.viewmodel  
Statistics com.example.myapp.ui.viewmodel  String com.example.myapp.ui.viewmodel  	ViewModel com.example.myapp.ui.viewmodel  _uiState com.example.myapp.ui.viewmodel  asStateFlow com.example.myapp.ui.viewmodel  	emptyList com.example.myapp.ui.viewmodel  launch com.example.myapp.ui.viewmodel  loadStatistics com.example.myapp.ui.viewmodel  
repository com.example.myapp.ui.viewmodel  update com.example.myapp.ui.viewmodel  bloodSugars 0com.example.myapp.ui.viewmodel.BloodSugarUiState  copy 0com.example.myapp.ui.viewmodel.BloodSugarUiState  errorMessage 0com.example.myapp.ui.viewmodel.BloodSugarUiState  	isLoading 0com.example.myapp.ui.viewmodel.BloodSugarUiState  
statistics 0com.example.myapp.ui.viewmodel.BloodSugarUiState  
BloodSugar 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  BloodSugarUiState 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  Calendar 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  MutableStateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  
Statistics 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  _uiState 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  
addBloodSugar 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  asStateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  
clearError 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  deleteBloodSugar 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  launch 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  loadBloodSugars 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  loadStatistics 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  
repository 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  uiState 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  update 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  updateBloodSugar 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  viewModelScope 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  average )com.example.myapp.ui.viewmodel.Statistics  count )com.example.myapp.ui.viewmodel.Statistics  max )com.example.myapp.ui.viewmodel.Statistics  min )com.example.myapp.ui.viewmodel.Statistics  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  Class 	java.lang  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  AlertDialog 	java.util  	Alignment 	java.util  Arrangement 	java.util  
BloodSugar 	java.util  BloodSugarRepository 	java.util  BloodSugarUiState 	java.util  Boolean 	java.util  Calendar 	java.util  Card 	java.util  CardDefaults 	java.util  Color 	java.util  Column 	java.util  
Composable 	java.util  Date 	java.util  DropdownMenuItem 	java.util  	Exception 	java.util  ExperimentalMaterial3Api 	java.util  ExposedDropdownMenuBox 	java.util  ExposedDropdownMenuDefaults 	java.util  Float 	java.util  
FontWeight 	java.util  
HiltViewModel 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  Inject 	java.util  Int 	java.util  KeyboardOptions 	java.util  KeyboardType 	java.util  List 	java.util  Locale 	java.util  
MaterialTheme 	java.util  MeasurementType 	java.util  Modifier 	java.util  MutableStateFlow 	java.util  OptIn 	java.util  OutlinedTextField 	java.util  Row 	java.util  SimpleDateFormat 	java.util  	StateFlow 	java.util  
Statistics 	java.util  String 	java.util  Text 	java.util  
TextButton 	java.util  TrailingIcon 	java.util  Unit 	java.util  	ViewModel 	java.util  _uiState 	java.util  asStateFlow 	java.util  
cardElevation 	java.util  	emptyList 	java.util  fillMaxWidth 	java.util  forEach 	java.util  getBloodSugarColor 	java.util  getValue 	java.util  
isNotEmpty 	java.util  launch 	java.util  loadStatistics 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  
repository 	java.util  setValue 	java.util  spacedBy 	java.util  
toFloatOrNull 	java.util  update 	java.util  weight 	java.util  DAY_OF_YEAR java.util.Calendar  add java.util.Calendar  getInstance java.util.Calendar  time java.util.Calendar  time java.util.Date  
getDefault java.util.Locale  Inject javax.inject  	Singleton javax.inject  Array kotlin  CharSequence kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function4 kotlin  Nothing kotlin  OptIn kotlin  String kotlin  let kotlin  synchronized kotlin  not kotlin.Boolean  sp 
kotlin.Double  String kotlin.Enum  message kotlin.Exception  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toString kotlin.Float  invoke kotlin.Function1  invoke kotlin.Function4  	compareTo 
kotlin.Int  minus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toString 
kotlin.Int  let kotlin.Long  	Companion 
kotlin.String  format 
kotlin.String  
isNotEmpty 
kotlin.String  let 
kotlin.String  
toFloatOrNull 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  List kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  
isNotEmpty kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  sortedBy kotlin.collections  take kotlin.collections  forEachIndexed kotlin.collections.List  isEmpty kotlin.collections.List  maxOf kotlin.collections.List  minOf kotlin.collections.List  size kotlin.collections.List  sortedBy kotlin.collections.List  take kotlin.collections.List  maxOf kotlin.comparisons  minOf kotlin.comparisons  SuspendFunction1 kotlin.coroutines  EnumEntries kotlin.enums  Volatile 
kotlin.jvm  java 
kotlin.jvm  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  sortedBy kotlin.sequences  take kotlin.sequences  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  
isNotEmpty kotlin.text  maxOf kotlin.text  minOf kotlin.text  take kotlin.text  
toFloatOrNull kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  
BloodSugar !kotlinx.coroutines.CoroutineScope  Calendar !kotlinx.coroutines.CoroutineScope  
Statistics !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadStatistics !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  update !kotlinx.coroutines.CoroutineScope  
BloodSugar kotlinx.coroutines.flow  BloodSugarRepository kotlinx.coroutines.flow  BloodSugarUiState kotlinx.coroutines.flow  Boolean kotlinx.coroutines.flow  Calendar kotlinx.coroutines.flow  Date kotlinx.coroutines.flow  	Exception kotlinx.coroutines.flow  Float kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  
HiltViewModel kotlinx.coroutines.flow  Inject kotlinx.coroutines.flow  Int kotlinx.coroutines.flow  List kotlinx.coroutines.flow  MeasurementType kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  
Statistics kotlinx.coroutines.flow  String kotlinx.coroutines.flow  	ViewModel kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  loadStatistics kotlinx.coroutines.flow  
repository kotlinx.coroutines.flow  update kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  update (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     