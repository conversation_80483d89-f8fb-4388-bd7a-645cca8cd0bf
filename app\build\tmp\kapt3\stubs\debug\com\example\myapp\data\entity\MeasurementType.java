package com.example.myapp.data.entity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/example/myapp/data/entity/MeasurementType;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "FASTING", "BEFORE_BREAKFAST", "AFTER_BREAKFAST", "BEFORE_LUNCH", "AFTER_LUNCH", "BEFORE_DINNER", "AFTER_DINNER", "BEFORE_SLEEP", "RANDOM", "app_debug"})
public enum MeasurementType {
    /*public static final*/ FASTING /* = new FASTING(null) */,
    /*public static final*/ BEFORE_BREAKFAST /* = new BEFORE_BREAKFAST(null) */,
    /*public static final*/ AFTER_BREAKFAST /* = new AFTER_BREAKFAST(null) */,
    /*public static final*/ BEFORE_LUNCH /* = new BEFORE_LUNCH(null) */,
    /*public static final*/ AFTER_LUNCH /* = new AFTER_LUNCH(null) */,
    /*public static final*/ BEFORE_DINNER /* = new BEFORE_DINNER(null) */,
    /*public static final*/ AFTER_DINNER /* = new AFTER_DINNER(null) */,
    /*public static final*/ BEFORE_SLEEP /* = new BEFORE_SLEEP(null) */,
    /*public static final*/ RANDOM /* = new RANDOM(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    MeasurementType(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.myapp.data.entity.MeasurementType> getEntries() {
        return null;
    }
}