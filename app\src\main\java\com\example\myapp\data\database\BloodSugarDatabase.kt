package com.example.myapp.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.example.myapp.data.converter.Converters
import com.example.myapp.data.dao.BloodSugarDao
import com.example.myapp.data.entity.BloodSugar

@Database(
    entities = [BloodSugar::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class BloodSugarDatabase : RoomDatabase() {
    
    abstract fun bloodSugarDao(): BloodSugarDao
    
    companion object {
        @Volatile
        private var INSTANCE: BloodSugarDatabase? = null
        
        fun getDatabase(context: Context): BloodSugarDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    BloodSugarDatabase::class.java,
                    "blood_sugar_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
