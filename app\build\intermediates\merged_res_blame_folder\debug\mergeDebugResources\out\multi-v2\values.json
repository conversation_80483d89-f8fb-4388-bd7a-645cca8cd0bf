{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.myapp-mergeDebugResources-57:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65304697de5524fed0d2857176f4f29\\transformed\\activity-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "84,103", "startColumns": "4,4", "startOffsets": "5131,6140", "endColumns": "41,59", "endOffsets": "5168,6195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\813812cb6119abfa1bf609e6960a602d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "79,83", "startColumns": "4,4", "startOffsets": "4887,5064", "endColumns": "53,66", "endOffsets": "4936,5126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fc5b8020ca22bcab73de0316db80dd2\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6254", "endColumns": "49", "endOffsets": "6299"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,675,722,769,816,861,906", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,717,764,811,856,901,943"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "41", "endOffsets": "53"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6574", "endColumns": "41", "endOffsets": "6611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdb9e3e29d5dc7261cbb30e01775346f\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,108,111,112,113,114,115,116,117,192,208,209,213,214,218,220,221,229,235,245,278,308,341", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,948,1018,1086,1158,1228,1289,1363,1436,1497,1558,1620,1684,1746,1807,1875,1975,2035,2101,2174,2243,2300,2352,2414,2486,2562,2627,2686,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3345,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4004,4063,4123,4183,4242,4301,4360,4419,4478,4941,4976,5238,5293,5356,5411,5469,5527,5588,5651,5708,5759,5809,5870,5927,5993,6027,6062,6421,6616,6683,6755,6824,6893,6967,7039,12286,13059,13176,13377,13487,13688,13899,13971,14342,14545,14846,16577,17577,18259", "endLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,108,111,112,113,114,115,116,117,192,208,212,213,217,218,220,221,234,244,277,298,340,346", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,607,670,1013,1081,1153,1223,1284,1358,1431,1492,1553,1615,1679,1741,1802,1870,1970,2030,2096,2169,2238,2295,2347,2409,2481,2557,2622,2681,2740,2800,2860,2920,2980,3040,3100,3160,3220,3280,3340,3399,3459,3519,3579,3639,3699,3759,3819,3879,3939,3999,4058,4118,4178,4237,4296,4355,4414,4473,4532,4971,5006,5288,5351,5406,5464,5522,5583,5646,5703,5754,5804,5865,5922,5988,6022,6057,6092,6486,6678,6750,6819,6888,6962,7034,7122,12352,13171,13372,13482,13683,13812,13966,14033,14540,14841,16572,17253,18254,18421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f0b3c638ab77a0ce9d8c56647651228\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "73,74,75,77,78,107,118,119,120,121,122,123,124,185,186,187,188,189,190,191,193,194,195,198,201,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4537,4611,4669,4781,4832,6368,7127,7192,7246,7312,7413,7471,7523,11950,12012,12066,12102,12136,12186,12240,12357,12404,12440,12641,12753,12864", "endLines": "73,74,75,77,78,107,118,119,120,121,122,123,124,185,186,187,188,189,190,191,193,194,195,200,203,207", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "4606,4664,4719,4827,4882,6416,7187,7241,7307,7408,7466,7518,7578,12007,12061,12097,12131,12181,12235,12281,12399,12435,12525,12748,12859,13054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3941d3d2533b646c43570924e053dea7\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6491", "endColumns": "82", "endOffsets": "6569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3cbcb034f323467ada735db62b22cdd0\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6200", "endColumns": "53", "endOffsets": "6249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc5ee51c3bec86ce8865a872b51c10b2\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "76,85,106,299,304", "startColumns": "4,4,4,4,4", "startOffsets": "4724,5173,6304,17258,17428", "endLines": "76,85,106,303,307", "endColumns": "56,64,63,24,24", "endOffsets": "4776,5233,6363,17423,17572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0797a0c48a4196698145cc6d62689bea\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6097", "endColumns": "42", "endOffsets": "6135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\db1b3ed539110de8f31b77a96ff3019b\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "347,360,366,372,381", "startColumns": "4,4,4,4,4", "startOffsets": "18426,19065,19309,19556,19919", "endLines": "359,365,371,374,385", "endColumns": "24,24,24,24,24", "endOffsets": "19060,19304,19551,19684,20096"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "82", "endOffsets": "134"}, "to": {"startLines": "219", "startColumns": "4", "startOffsets": "13817", "endColumns": "81", "endOffsets": "13894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\476a154153ced0f5af1c9393881cd727\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "82,222,375,378", "startColumns": "4,4,4,4", "startOffsets": "5011,14038,19689,19804", "endLines": "82,228,377,380", "endColumns": "52,24,24,24", "endOffsets": "5059,14337,19799,19914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\195d9e3dd5a9d6ba896447ff14efbb05\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "125,126,127,128,129,130,131,132,133,134,137,138,139,140,141,142,143,144,145,146,147,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7583,7671,7757,7838,7922,7991,8056,8139,8245,8331,8451,8505,8574,8635,8704,8793,8888,8962,9059,9152,9250,9399,9490,9578,9674,9772,9836,9904,9991,10085,10152,10224,10296,10397,10506,10582,10651,10699,10765,10829,10886,10943,11015,11065,11119,11190,11261,11331,11400,11458,11534,11605,11679,11765,11815,11885", "endLines": "125,126,127,128,129,130,131,132,133,136,137,138,139,140,141,142,143,144,145,146,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "7666,7752,7833,7917,7986,8051,8134,8240,8326,8446,8500,8569,8630,8699,8788,8883,8957,9054,9147,9245,9394,9485,9573,9669,9767,9831,9899,9986,10080,10147,10219,10291,10392,10501,10577,10646,10694,10760,10824,10881,10938,11010,11060,11114,11185,11256,11326,11395,11453,11529,11600,11674,11760,11810,11880,11945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36cdb26bea2ca7fcb7382d8ae39b5662\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "196,197", "startColumns": "4,4", "startOffsets": "12530,12586", "endColumns": "55,54", "endOffsets": "12581,12636"}}]}, {"outputFile": "com.example.myapp-mergeDebugResources-57:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65304697de5524fed0d2857176f4f29\\transformed\\activity-1.8.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "84,103", "startColumns": "4,4", "startOffsets": "5131,6140", "endColumns": "41,59", "endOffsets": "5168,6195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\813812cb6119abfa1bf609e6960a602d\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "79,83", "startColumns": "4,4", "startOffsets": "4887,5064", "endColumns": "53,66", "endOffsets": "4936,5126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fc5b8020ca22bcab73de0316db80dd2\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6254", "endColumns": "49", "endOffsets": "6299"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,675,722,769,816,861,906", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,717,764,811,856,901,943"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "45", "endOffsets": "57"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6574", "endColumns": "45", "endOffsets": "6615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdb9e3e29d5dc7261cbb30e01775346f\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,108,111,112,113,114,115,116,117,192,208,209,213,214,218,220,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,948,1018,1086,1158,1228,1289,1363,1436,1497,1558,1620,1684,1746,1807,1875,1975,2035,2101,2174,2243,2300,2352,2414,2486,2562,2627,2686,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3345,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4004,4063,4123,4183,4242,4301,4360,4419,4478,4941,4976,5238,5293,5356,5411,5469,5527,5588,5651,5708,5759,5809,5870,5927,5993,6027,6062,6421,6620,6687,6759,6828,6897,6971,7043,12290,13063,13180,13381,13491,13692,13903,13975", "endLines": "2,3,4,6,7,8,9,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,80,81,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,108,111,112,113,114,115,116,117,192,208,212,213,217,218,220,221", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "205,277,365,472,538,607,670,1013,1081,1153,1223,1284,1358,1431,1492,1553,1615,1679,1741,1802,1870,1970,2030,2096,2169,2238,2295,2347,2409,2481,2557,2622,2681,2740,2800,2860,2920,2980,3040,3100,3160,3220,3280,3340,3399,3459,3519,3579,3639,3699,3759,3819,3879,3939,3999,4058,4118,4178,4237,4296,4355,4414,4473,4532,4971,5006,5288,5351,5406,5464,5522,5583,5646,5703,5754,5804,5865,5922,5988,6022,6057,6092,6486,6682,6754,6823,6892,6966,7038,7126,12356,13175,13376,13486,13687,13816,13970,14037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f0b3c638ab77a0ce9d8c56647651228\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "73,74,75,77,78,107,118,119,120,121,122,123,124,185,186,187,188,189,190,191,193,194,195,198,201,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4537,4611,4669,4781,4832,6368,7131,7196,7250,7316,7417,7475,7527,11954,12016,12070,12106,12140,12190,12244,12361,12408,12444,12645,12757,12868", "endLines": "73,74,75,77,78,107,118,119,120,121,122,123,124,185,186,187,188,189,190,191,193,194,195,200,203,207", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "4606,4664,4719,4827,4882,6416,7191,7245,7311,7412,7470,7522,7582,12011,12065,12101,12135,12185,12239,12285,12403,12439,12529,12752,12863,13058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3941d3d2533b646c43570924e053dea7\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6491", "endColumns": "82", "endOffsets": "6569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3cbcb034f323467ada735db62b22cdd0\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6200", "endColumns": "53", "endOffsets": "6249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc5ee51c3bec86ce8865a872b51c10b2\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "76,85,106", "startColumns": "4,4,4", "startOffsets": "4724,5173,6304", "endColumns": "56,64,63", "endOffsets": "4776,5233,6363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0797a0c48a4196698145cc6d62689bea\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6097", "endColumns": "42", "endOffsets": "6135"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "219", "startColumns": "4", "startOffsets": "13821", "endColumns": "81", "endOffsets": "13898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\476a154153ced0f5af1c9393881cd727\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "5011", "endColumns": "52", "endOffsets": "5059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\195d9e3dd5a9d6ba896447ff14efbb05\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "125,126,127,128,129,130,131,132,133,134,137,138,139,140,141,142,143,144,145,146,147,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7587,7675,7761,7842,7926,7995,8060,8143,8249,8335,8455,8509,8578,8639,8708,8797,8892,8966,9063,9156,9254,9403,9494,9582,9678,9776,9840,9908,9995,10089,10156,10228,10300,10401,10510,10586,10655,10703,10769,10833,10890,10947,11019,11069,11123,11194,11265,11335,11404,11462,11538,11609,11683,11769,11819,11889", "endLines": "125,126,127,128,129,130,131,132,133,136,137,138,139,140,141,142,143,144,145,146,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "7670,7756,7837,7921,7990,8055,8138,8244,8330,8450,8504,8573,8634,8703,8792,8887,8961,9058,9151,9249,9398,9489,9577,9673,9771,9835,9903,9990,10084,10151,10223,10295,10396,10505,10581,10650,10698,10764,10828,10885,10942,11014,11064,11118,11189,11260,11330,11399,11457,11533,11604,11678,11764,11814,11884,11949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36cdb26bea2ca7fcb7382d8ae39b5662\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "196,197", "startColumns": "4,4", "startOffsets": "12534,12590", "endColumns": "55,54", "endOffsets": "12585,12640"}}]}]}