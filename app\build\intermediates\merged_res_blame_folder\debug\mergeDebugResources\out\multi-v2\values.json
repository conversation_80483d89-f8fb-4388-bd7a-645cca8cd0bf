{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf82c8696474a41958f2166ec62a4e84\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2091,2107,2113,3153,3169", "startColumns": "4,4,4,4,4", "startOffsets": "137376,137801,137979,172864,173275", "endLines": "2106,2112,2122,3168,3172", "endColumns": "24,24,24,24,24", "endOffsets": "137796,137974,138258,173270,173397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\96c714894ab5cd269fe1203f19891e98\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "297", "startColumns": "4", "startOffsets": "18903", "endColumns": "42", "endOffsets": "18941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c9470c3f614b047e679c5fb76bbe16ca\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "299", "startColumns": "4", "startOffsets": "19006", "endColumns": "53", "endOffsets": "19055"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,85,86,87,98,99,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1956,5514,5561,5608,6352,6397,6563", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1993,5556,5603,5650,6392,6437,6600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4e797455487c547913a2c104354343cb\\transformed\\activity-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "277,298", "startColumns": "4,4", "startOffsets": "17859,18946", "endColumns": "41,59", "endOffsets": "17896,19001"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "45", "endOffsets": "57"}, "to": {"startLines": "337", "startColumns": "4", "startOffsets": "21557", "endColumns": "45", "endOffsets": "21598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d0effdc45e183036880fc61940e99b3\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,43,44,75,76,184,185,186,187,188,189,190,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,271,272,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,308,338,339,340,341,342,343,344,438,1813,1814,1818,1819,1823,1968,1969,2622,2656,2712,2745,2775,2808", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1477,1549,2639,2704,4794,4863,12054,12124,12192,12264,12334,12395,12469,13326,13387,13448,13510,13574,13636,13697,13765,13865,13925,13991,14064,14133,14190,14242,14757,14829,14905,14970,15029,15088,15148,15208,15268,15328,15388,15448,15508,15568,15628,15688,15747,15807,15867,15927,15987,16047,16107,16167,16227,16287,16347,16406,16466,16526,16585,16644,16703,16762,16821,17573,17608,18012,18067,18130,18185,18243,18301,18362,18425,18482,18533,18583,18644,18701,18767,18801,18836,19538,21603,21670,21742,21811,21880,21954,22026,29659,120758,120875,121076,121186,121387,132893,132965,154465,156038,158268,159999,160999,161681", "endLines": "9,28,29,43,44,75,76,184,185,186,187,188,189,190,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,271,272,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,308,338,339,340,341,342,343,344,438,1813,1817,1818,1822,1823,1968,1969,2627,2665,2744,2765,2807,2813", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1544,1632,2699,2765,4858,4921,12119,12187,12259,12329,12390,12464,12537,13382,13443,13505,13569,13631,13692,13760,13860,13920,13986,14059,14128,14185,14237,14299,14824,14900,14965,15024,15083,15143,15203,15263,15323,15383,15443,15503,15563,15623,15683,15742,15802,15862,15922,15982,16042,16102,16162,16222,16282,16342,16401,16461,16521,16580,16639,16698,16757,16816,16875,17603,17638,18062,18125,18180,18238,18296,18357,18420,18477,18528,18578,18639,18696,18762,18796,18831,18866,19603,21665,21737,21806,21875,21949,22021,22109,29725,120870,121071,121181,121382,121511,132960,133027,154663,156334,159994,160675,161676,161843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\986e590fdb6b3dc92ec7ee7a376aa7c0\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "270,274", "startColumns": "4,4", "startOffsets": "17519,17696", "endColumns": "53,66", "endOffsets": "17568,17758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f079b45269aea34bce26c98c18be4af6\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3077", "startColumns": "4,4,4", "startOffsets": "250,511,170694", "endLines": "7,17,3080", "endColumns": "11,11,24", "endOffsets": "397,813,170832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abc133dde975a2a52fdd6f2f6c3ad94d\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "45,46,47,48,49,50,51,52,347,348,349,350,351,352,353,354,356,357,358,359,360,361,362,363,364,2850,3122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2770,2860,2940,3030,3120,3200,3281,3361,22233,22338,22519,22644,22751,22931,23054,23170,23440,23628,23733,23914,24039,24214,24362,24425,24487,163061,172135", "endLines": "45,46,47,48,49,50,51,52,347,348,349,350,351,352,353,354,356,357,358,359,360,361,362,363,364,2862,3140", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2855,2935,3025,3115,3195,3276,3356,3436,22333,22514,22639,22746,22926,23049,23165,23268,23623,23728,23909,24034,24209,24357,24420,24482,24561,163371,172547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e00bb0cb13b50e7a03e9d56b3ab5729\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24903,24991,25077,25158,25242,25311,25376,25459,25565,25651,25771,25825,25894,25955,26024,26113,26208,26282,26379,26472,26570,26719,26810,26898,26994,27092,27156,27224,27311,27405,27472,27544,27616,27717,27826,27902,27971,28019,28085,28149,28206,28263,28335,28385,28439,28510,28581,28651,28720,28778,28854,28925,28999,29085,29135,29205", "endLines": "370,371,372,373,374,375,376,377,378,381,382,383,384,385,386,387,388,389,390,391,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "24986,25072,25153,25237,25306,25371,25454,25560,25646,25766,25820,25889,25950,26019,26108,26203,26277,26374,26467,26565,26714,26805,26893,26989,27087,27151,27219,27306,27400,27467,27539,27611,27712,27821,27897,27966,28014,28080,28144,28201,28258,28330,28380,28434,28505,28576,28646,28715,28773,28849,28920,28994,29080,29130,29200,29265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0233180bfb2a73e0304a16e4c911a11f\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "262,264,265,267,269,302,345,346,365,366,367,368,369,430,431,432,433,434,435,437,439,440,441,1532,1535,1538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17085,17209,17267,17379,17464,19174,22114,22179,24566,24632,24733,24791,24843,29270,29332,29386,29422,29456,29506,29613,29730,29777,29813,99454,99566,99677", "endLines": "262,264,265,267,269,302,345,346,365,366,367,368,369,430,431,432,433,434,435,437,439,440,441,1534,1537,1541", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "17154,17262,17317,17425,17514,19222,22174,22228,24627,24728,24786,24838,24898,29327,29381,29417,29451,29501,29555,29654,29772,29808,29898,99561,99672,99867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0a02135d11c09939ed83bdc229c6e722\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "266,278,301,2766,2771", "startColumns": "4,4,4,4,4", "startOffsets": "17322,17901,19110,160680,160850", "endLines": "266,278,301,2770,2774", "endColumns": "56,64,63,24,24", "endOffsets": "17374,17961,19169,160845,160994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a76451986ca9c11d04c7076059da51\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "307,355", "startColumns": "4,4", "startOffsets": "19470,23273", "endColumns": "67,166", "endOffsets": "19533,23435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6740d9c1d5010b3ff56ab15c10b28894\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "336", "startColumns": "4", "startOffsets": "21474", "endColumns": "82", "endOffsets": "21552"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Myapp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "82", "endOffsets": "134"}, "to": {"startLines": "1860", "startColumns": "4", "startOffsets": "124152", "endColumns": "81", "endOffsets": "124229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22f038d89e49e38792143e9fe11d5050\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,191,192,193,194,195,196,197,198,199,215,216,217,218,219,220,221,222,258,259,260,261,268,275,276,279,296,303,304,305,306,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,436,444,445,446,447,448,449,457,458,462,466,470,475,481,488,492,496,501,505,509,513,517,521,525,531,535,541,545,551,555,560,564,567,571,577,581,587,591,597,600,604,608,612,616,620,621,622,623,626,629,632,635,639,640,641,642,643,646,648,650,652,657,658,662,668,672,673,675,687,688,692,698,702,703,704,708,735,739,740,744,772,944,970,1141,1167,1198,1206,1212,1228,1250,1255,1260,1270,1279,1288,1292,1299,1318,1325,1326,1335,1338,1341,1345,1349,1353,1356,1357,1362,1367,1377,1382,1389,1395,1396,1399,1403,1408,1410,1412,1415,1418,1420,1424,1427,1434,1437,1440,1444,1446,1450,1452,1454,1456,1460,1468,1476,1488,1494,1503,1506,1517,1520,1521,1526,1527,1542,1611,1681,1682,1692,1701,1702,1704,1708,1711,1714,1717,1720,1723,1726,1729,1733,1736,1739,1742,1746,1749,1753,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1779,1781,1782,1783,1784,1785,1786,1787,1788,1790,1791,1793,1794,1796,1798,1799,1801,1802,1803,1804,1805,1806,1808,1809,1810,1811,1812,1824,1826,1828,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1844,1845,1846,1847,1848,1849,1850,1852,1856,1861,1862,1863,1864,1865,1866,1870,1871,1872,1873,1875,1877,1879,1881,1883,1884,1885,1886,1888,1890,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1906,1907,1908,1909,1911,1913,1914,1916,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1936,1937,1938,1939,1941,1942,1943,1944,1945,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1970,2045,2048,2051,2054,2068,2081,2123,2126,2155,2182,2191,2255,2618,2628,2666,2694,2814,2838,2844,2863,2884,3008,3067,3073,3081,3087,3141,3173,3239,3259,3314,3326,3352", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,976,1040,1110,1171,1246,1322,1399,1637,1722,1804,1880,1998,2075,2153,2259,2365,2444,2524,2581,3441,3515,3590,3655,3721,3781,3842,3914,3987,4054,4122,4181,4240,4299,4358,4417,4471,4525,4578,4632,4686,4740,4926,5000,5079,5152,5226,5297,5369,5441,5655,5712,5770,5843,5917,5991,6066,6138,6211,6281,6442,6502,6605,6674,6743,6813,6887,6963,7027,7104,7180,7257,7322,7391,7468,7543,7612,7680,7757,7823,7884,7981,8046,8115,8214,8285,8344,8402,8459,8518,8582,8653,8725,8797,8869,8941,9008,9076,9144,9203,9266,9330,9420,9511,9571,9637,9704,9770,9840,9904,9957,10024,10085,10152,10265,10323,10386,10451,10516,10591,10664,10736,10780,10827,10873,10922,10983,11044,11105,11167,11231,11295,11359,11424,11487,11547,11608,11674,11733,11793,11855,11926,11986,12542,12628,12715,12805,12892,12980,13062,13145,13235,14304,14356,14414,14459,14525,14589,14646,14703,16880,16937,16985,17034,17430,17763,17810,17966,18871,19227,19291,19353,19413,19608,19682,19752,19830,19884,19954,20039,20087,20133,20194,20257,20323,20387,20458,20521,20586,20650,20711,20772,20824,20897,20971,21040,21115,21189,21263,21404,29560,30014,30092,30182,30270,30366,30456,31038,31127,31374,31655,31907,32192,32585,33062,33284,33506,33782,34009,34239,34469,34699,34929,35156,35575,35801,36226,36456,36884,37103,37386,37594,37725,37952,38378,38603,39030,39251,39676,39796,40072,40373,40697,40988,41302,41439,41570,41675,41917,42084,42288,42496,42767,42879,42991,43096,43213,43427,43573,43713,43799,44147,44235,44481,44899,45148,45230,45328,45985,46085,46337,46761,47016,47110,47199,47436,49460,49702,49804,50057,52213,62894,64410,75105,76633,78390,79016,79436,80697,81962,82218,82454,83001,83495,84100,84298,84878,86246,86621,86739,87277,87434,87630,87903,88159,88329,88470,88534,88899,89266,89942,90206,90544,90897,90991,91177,91483,91745,91870,91997,92236,92447,92566,92759,92936,93391,93572,93694,93953,94066,94253,94355,94462,94591,94866,95374,95870,96747,97041,97611,97760,98492,98664,98748,99084,99176,99872,105103,110474,110536,111114,111698,111789,111902,112131,112291,112443,112614,112780,112949,113116,113279,113522,113692,113865,114036,114310,114509,114714,115044,115128,115224,115320,115418,115518,115620,115722,115824,115926,116028,116128,116224,116336,116465,116588,116719,116850,116948,117062,117156,117296,117430,117526,117638,117738,117854,117950,118062,118162,118302,118438,118602,118732,118890,119040,119181,119325,119460,119572,119722,119850,119978,120114,120246,120376,120506,120618,121516,121662,121806,121944,122010,122100,122176,122280,122370,122472,122580,122688,122788,122868,122960,123058,123168,123220,123298,123404,123496,123600,123710,123832,123995,124234,124314,124414,124504,124614,124704,124945,125039,125145,125237,125337,125449,125563,125679,125795,125889,126003,126115,126217,126337,126459,126541,126645,126765,126891,126989,127083,127171,127283,127399,127521,127633,127808,127924,128010,128102,128214,128338,128405,128531,128599,128727,128871,128999,129068,129163,129278,129391,129490,129599,129710,129821,129922,130027,130127,130257,130348,130471,130565,130677,130763,130867,130963,131051,131169,131273,131377,131503,131591,131699,131799,131889,131999,132083,132185,132269,132323,132387,132493,132579,132689,132773,133032,135648,135766,135881,135961,136322,136859,138263,138341,139685,141046,141434,144277,154330,154668,156339,157696,161848,162599,162861,163376,163755,168033,170314,170543,170837,171052,172552,173402,176428,177172,179303,179643,180954", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,191,192,193,194,195,196,197,198,199,215,216,217,218,219,220,221,222,258,259,260,261,268,275,276,279,296,303,304,305,306,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,436,444,445,446,447,448,456,457,461,465,469,474,480,487,491,495,500,504,508,512,516,520,524,530,534,540,544,550,554,559,563,566,570,576,580,586,590,596,599,603,607,611,615,619,620,621,622,625,628,631,634,638,639,640,641,642,645,647,649,651,656,657,661,667,671,672,674,686,687,691,697,701,702,703,707,734,738,739,743,771,943,969,1140,1166,1197,1205,1211,1227,1249,1254,1259,1269,1278,1287,1291,1298,1317,1324,1325,1334,1337,1340,1344,1348,1352,1355,1356,1361,1366,1376,1381,1388,1394,1395,1398,1402,1407,1409,1411,1414,1417,1419,1423,1426,1433,1436,1439,1443,1445,1449,1451,1453,1455,1459,1467,1475,1487,1493,1502,1505,1516,1519,1520,1525,1526,1531,1610,1680,1681,1691,1700,1701,1703,1707,1710,1713,1716,1719,1722,1725,1728,1732,1735,1738,1741,1745,1748,1752,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1778,1780,1781,1782,1783,1784,1785,1786,1787,1789,1790,1792,1793,1795,1797,1798,1800,1801,1802,1803,1804,1805,1807,1808,1809,1810,1811,1812,1825,1827,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1843,1844,1845,1846,1847,1848,1849,1851,1855,1859,1861,1862,1863,1864,1865,1869,1870,1871,1872,1874,1876,1878,1880,1882,1883,1884,1885,1887,1889,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1905,1906,1907,1908,1910,1912,1913,1915,1916,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1935,1936,1937,1938,1940,1941,1942,1943,1944,1946,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,2044,2047,2050,2053,2067,2073,2090,2125,2154,2181,2190,2254,2617,2621,2655,2693,2711,2837,2843,2849,2883,3007,3027,3072,3076,3086,3121,3152,3238,3258,3313,3325,3351,3358", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,971,1035,1105,1166,1241,1317,1394,1472,1717,1799,1875,1951,2070,2148,2254,2360,2439,2519,2576,2634,3510,3585,3650,3716,3776,3837,3909,3982,4049,4117,4176,4235,4294,4353,4412,4466,4520,4573,4627,4681,4735,4789,4995,5074,5147,5221,5292,5364,5436,5509,5707,5765,5838,5912,5986,6061,6133,6206,6276,6347,6497,6558,6669,6738,6808,6882,6958,7022,7099,7175,7252,7317,7386,7463,7538,7607,7675,7752,7818,7879,7976,8041,8110,8209,8280,8339,8397,8454,8513,8577,8648,8720,8792,8864,8936,9003,9071,9139,9198,9261,9325,9415,9506,9566,9632,9699,9765,9835,9899,9952,10019,10080,10147,10260,10318,10381,10446,10511,10586,10659,10731,10775,10822,10868,10917,10978,11039,11100,11162,11226,11290,11354,11419,11482,11542,11603,11669,11728,11788,11850,11921,11981,12049,12623,12710,12800,12887,12975,13057,13140,13230,13321,14351,14409,14454,14520,14584,14641,14698,14752,16932,16980,17029,17080,17459,17805,17854,18007,18898,19286,19348,19408,19465,19677,19747,19825,19879,19949,20034,20082,20128,20189,20252,20318,20382,20453,20516,20581,20645,20706,20767,20819,20892,20966,21035,21110,21184,21258,21399,21469,29608,30087,30177,30265,30361,30451,31033,31122,31369,31650,31902,32187,32580,33057,33279,33501,33777,34004,34234,34464,34694,34924,35151,35570,35796,36221,36451,36879,37098,37381,37589,37720,37947,38373,38598,39025,39246,39671,39791,40067,40368,40692,40983,41297,41434,41565,41670,41912,42079,42283,42491,42762,42874,42986,43091,43208,43422,43568,43708,43794,44142,44230,44476,44894,45143,45225,45323,45980,46080,46332,46756,47011,47105,47194,47431,49455,49697,49799,50052,52208,62889,64405,75100,76628,78385,79011,79431,80692,81957,82213,82449,82996,83490,84095,84293,84873,86241,86616,86734,87272,87429,87625,87898,88154,88324,88465,88529,88894,89261,89937,90201,90539,90892,90986,91172,91478,91740,91865,91992,92231,92442,92561,92754,92931,93386,93567,93689,93948,94061,94248,94350,94457,94586,94861,95369,95865,96742,97036,97606,97755,98487,98659,98743,99079,99171,99449,105098,110469,110531,111109,111693,111784,111897,112126,112286,112438,112609,112775,112944,113111,113274,113517,113687,113860,114031,114305,114504,114709,115039,115123,115219,115315,115413,115513,115615,115717,115819,115921,116023,116123,116219,116331,116460,116583,116714,116845,116943,117057,117151,117291,117425,117521,117633,117733,117849,117945,118057,118157,118297,118433,118597,118727,118885,119035,119176,119320,119455,119567,119717,119845,119973,120109,120241,120371,120501,120613,120753,121657,121801,121939,122005,122095,122171,122275,122365,122467,122575,122683,122783,122863,122955,123053,123163,123215,123293,123399,123491,123595,123705,123827,123990,124147,124309,124409,124499,124609,124699,124940,125034,125140,125232,125332,125444,125558,125674,125790,125884,125998,126110,126212,126332,126454,126536,126640,126760,126886,126984,127078,127166,127278,127394,127516,127628,127803,127919,128005,128097,128209,128333,128400,128526,128594,128722,128866,128994,129063,129158,129273,129386,129485,129594,129705,129816,129917,130022,130122,130252,130343,130466,130560,130672,130758,130862,130958,131046,131164,131268,131372,131498,131586,131694,131794,131884,131994,132078,132180,132264,132318,132382,132488,132574,132684,132768,132888,135643,135761,135876,135956,136317,136550,137371,138336,139680,141041,141429,144272,154325,154460,156033,157691,158263,162594,162856,163056,163750,168028,168634,170538,170689,171047,172130,172859,176423,177167,179298,179638,180949,181152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d4cabd05e69097012b1333f09dc2ff02\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "263", "startColumns": "4", "startOffsets": "17159", "endColumns": "49", "endOffsets": "17204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86b2aff77130131bb6ebfada36b55526\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "442,443", "startColumns": "4,4", "startOffsets": "29903,29959", "endColumns": "55,54", "endOffsets": "29954,30009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ee2433b7bb74b77477cc8b19209c4d51\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "300", "startColumns": "4", "startOffsets": "19060", "endColumns": "49", "endOffsets": "19105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2610ed84e41f60c49b1fa5496ab99040\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3028,3041,3047,3053,3062", "startColumns": "4,4,4,4,4", "startOffsets": "168639,169278,169522,169769,170132", "endLines": "3040,3046,3052,3055,3066", "endColumns": "24,24,24,24,24", "endOffsets": "169273,169517,169764,169897,170309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a67828f4885dfeea796e22b6c8f62f02\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "273,2074,3056,3059", "startColumns": "4,4,4,4", "startOffsets": "17643,136555,169902,170017", "endLines": "273,2080,3058,3061", "endColumns": "52,24,24,24", "endOffsets": "17691,136854,170012,170127"}}]}]}