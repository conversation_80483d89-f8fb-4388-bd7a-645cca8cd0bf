  Activity android.app  Application android.app  Bundle android.app.Activity  Context android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  Canvas androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Add &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  Date androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Surface androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  Date androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Offset androidx.compose.ui.geometry  Color androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  Stroke &androidx.compose.ui.graphics.drawscope  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  compose androidx.lifecycle  viewModelScope androidx.lifecycle  
BloodSugar androidx.lifecycle.ViewModel  BloodSugarRepository androidx.lifecycle.ViewModel  BloodSugarUiState androidx.lifecycle.ViewModel  Date androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  MeasurementType androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  
BloodSugarDao androidx.room.RoomDatabase  BloodSugarDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  BloodSugarApplication com.example.myapp  MainActivity com.example.myapp  Bundle com.example.myapp.MainActivity  
Converters  com.example.myapp.data.converter  Long  com.example.myapp.data.converter  String  com.example.myapp.data.converter  Date +com.example.myapp.data.converter.Converters  Long +com.example.myapp.data.converter.Converters  MeasurementType +com.example.myapp.data.converter.Converters  String +com.example.myapp.data.converter.Converters  
TypeConverter +com.example.myapp.data.converter.Converters  
BloodSugarDao com.example.myapp.data.dao  Dao com.example.myapp.data.dao  Delete com.example.myapp.data.dao  Float com.example.myapp.data.dao  Insert com.example.myapp.data.dao  Int com.example.myapp.data.dao  List com.example.myapp.data.dao  Long com.example.myapp.data.dao  Query com.example.myapp.data.dao  Update com.example.myapp.data.dao  
BloodSugar (com.example.myapp.data.dao.BloodSugarDao  Date (com.example.myapp.data.dao.BloodSugarDao  Delete (com.example.myapp.data.dao.BloodSugarDao  Float (com.example.myapp.data.dao.BloodSugarDao  Flow (com.example.myapp.data.dao.BloodSugarDao  Insert (com.example.myapp.data.dao.BloodSugarDao  Int (com.example.myapp.data.dao.BloodSugarDao  List (com.example.myapp.data.dao.BloodSugarDao  Long (com.example.myapp.data.dao.BloodSugarDao  MeasurementType (com.example.myapp.data.dao.BloodSugarDao  Query (com.example.myapp.data.dao.BloodSugarDao  Update (com.example.myapp.data.dao.BloodSugarDao  deleteBloodSugar (com.example.myapp.data.dao.BloodSugarDao  deleteBloodSugarById (com.example.myapp.data.dao.BloodSugarDao  updateBloodSugar (com.example.myapp.data.dao.BloodSugarDao  
BloodSugar com.example.myapp.data.database  BloodSugarDatabase com.example.myapp.data.database  
Converters com.example.myapp.data.database  Volatile com.example.myapp.data.database  
BloodSugarDao 2com.example.myapp.data.database.BloodSugarDatabase  BloodSugarDatabase 2com.example.myapp.data.database.BloodSugarDatabase  Context 2com.example.myapp.data.database.BloodSugarDatabase  Volatile 2com.example.myapp.data.database.BloodSugarDatabase  
BloodSugarDao <com.example.myapp.data.database.BloodSugarDatabase.Companion  BloodSugarDatabase <com.example.myapp.data.database.BloodSugarDatabase.Companion  Context <com.example.myapp.data.database.BloodSugarDatabase.Companion  Volatile <com.example.myapp.data.database.BloodSugarDatabase.Companion  
BloodSugar com.example.myapp.data.entity  Float com.example.myapp.data.entity  Long com.example.myapp.data.entity  MeasurementType com.example.myapp.data.entity  String com.example.myapp.data.entity  Date (com.example.myapp.data.entity.BloodSugar  Float (com.example.myapp.data.entity.BloodSugar  Long (com.example.myapp.data.entity.BloodSugar  MeasurementType (com.example.myapp.data.entity.BloodSugar  
PrimaryKey (com.example.myapp.data.entity.BloodSugar  String (com.example.myapp.data.entity.BloodSugar  MeasurementType -com.example.myapp.data.entity.MeasurementType  String -com.example.myapp.data.entity.MeasurementType  BloodSugarRepository !com.example.myapp.data.repository  Float !com.example.myapp.data.repository  Int !com.example.myapp.data.repository  List !com.example.myapp.data.repository  Long !com.example.myapp.data.repository  
BloodSugar 6com.example.myapp.data.repository.BloodSugarRepository  
BloodSugarDao 6com.example.myapp.data.repository.BloodSugarRepository  Date 6com.example.myapp.data.repository.BloodSugarRepository  Float 6com.example.myapp.data.repository.BloodSugarRepository  Flow 6com.example.myapp.data.repository.BloodSugarRepository  Inject 6com.example.myapp.data.repository.BloodSugarRepository  Int 6com.example.myapp.data.repository.BloodSugarRepository  List 6com.example.myapp.data.repository.BloodSugarRepository  Long 6com.example.myapp.data.repository.BloodSugarRepository  MeasurementType 6com.example.myapp.data.repository.BloodSugarRepository  
bloodSugarDao 6com.example.myapp.data.repository.BloodSugarRepository  DatabaseModule com.example.myapp.di  SingletonComponent com.example.myapp.di  ApplicationContext #com.example.myapp.di.DatabaseModule  
BloodSugarDao #com.example.myapp.di.DatabaseModule  BloodSugarDatabase #com.example.myapp.di.DatabaseModule  Context #com.example.myapp.di.DatabaseModule  Provides #com.example.myapp.di.DatabaseModule  	Singleton #com.example.myapp.di.DatabaseModule  AddBloodSugarDialog com.example.myapp.ui.components  BloodSugarChart com.example.myapp.ui.components  BloodSugarItem com.example.myapp.ui.components  
Composable com.example.myapp.ui.components  Date com.example.myapp.ui.components  EditBloodSugarDialog com.example.myapp.ui.components  ExperimentalMaterial3Api com.example.myapp.ui.components  Float com.example.myapp.ui.components  List com.example.myapp.ui.components  OptIn com.example.myapp.ui.components  SimpleLineChart com.example.myapp.ui.components  
StatisticItem com.example.myapp.ui.components  StatisticsCard com.example.myapp.ui.components  String com.example.myapp.ui.components  Unit com.example.myapp.ui.components  getBloodSugarColor com.example.myapp.ui.components  
Composable com.example.myapp.ui.screen  ExperimentalMaterial3Api com.example.myapp.ui.screen  
HomeScreen com.example.myapp.ui.screen  OptIn com.example.myapp.ui.screen  Boolean com.example.myapp.ui.theme  DarkColorScheme com.example.myapp.ui.theme  LightColorScheme com.example.myapp.ui.theme  
MyappTheme com.example.myapp.ui.theme  Pink40 com.example.myapp.ui.theme  Pink80 com.example.myapp.ui.theme  Purple40 com.example.myapp.ui.theme  Purple80 com.example.myapp.ui.theme  PurpleGrey40 com.example.myapp.ui.theme  PurpleGrey80 com.example.myapp.ui.theme  
Typography com.example.myapp.ui.theme  Unit com.example.myapp.ui.theme  BloodSugarUiState com.example.myapp.ui.viewmodel  BloodSugarViewModel com.example.myapp.ui.viewmodel  Boolean com.example.myapp.ui.viewmodel  Date com.example.myapp.ui.viewmodel  Float com.example.myapp.ui.viewmodel  Int com.example.myapp.ui.viewmodel  List com.example.myapp.ui.viewmodel  MutableStateFlow com.example.myapp.ui.viewmodel  	StateFlow com.example.myapp.ui.viewmodel  
Statistics com.example.myapp.ui.viewmodel  String com.example.myapp.ui.viewmodel  asStateFlow com.example.myapp.ui.viewmodel  
BloodSugar 0com.example.myapp.ui.viewmodel.BloodSugarUiState  Boolean 0com.example.myapp.ui.viewmodel.BloodSugarUiState  List 0com.example.myapp.ui.viewmodel.BloodSugarUiState  
Statistics 0com.example.myapp.ui.viewmodel.BloodSugarUiState  String 0com.example.myapp.ui.viewmodel.BloodSugarUiState  
BloodSugar 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  BloodSugarRepository 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  BloodSugarUiState 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  Date 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  Float 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  Inject 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  MeasurementType 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  MutableStateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  	StateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  String 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  _uiState 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  asStateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  getASStateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  getAsStateFlow 2com.example.myapp.ui.viewmodel.BloodSugarViewModel  Float )com.example.myapp.ui.viewmodel.Statistics  Int )com.example.myapp.ui.viewmodel.Statistics  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  
BloodSugar 	java.lang  BloodSugarUiState 	java.lang  
Converters 	java.lang  ExperimentalMaterial3Api 	java.lang  MutableStateFlow 	java.lang  SingletonComponent 	java.lang  asStateFlow 	java.lang  SimpleDateFormat 	java.text  BloodSugarUiState 	java.util  
Composable 	java.util  Date 	java.util  ExperimentalMaterial3Api 	java.util  MutableStateFlow 	java.util  	StateFlow 	java.util  asStateFlow 	java.util  Inject javax.inject  	Singleton javax.inject  Array kotlin  
BloodSugar kotlin  BloodSugarUiState kotlin  Boolean kotlin  
Converters kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  Volatile kotlin  arrayOf kotlin  asStateFlow kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  
BloodSugar kotlin.annotation  BloodSugarUiState kotlin.annotation  
Converters kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  MutableStateFlow kotlin.annotation  SingletonComponent kotlin.annotation  Volatile kotlin.annotation  asStateFlow kotlin.annotation  
BloodSugar kotlin.collections  BloodSugarUiState kotlin.collections  
Converters kotlin.collections  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  MutableStateFlow kotlin.collections  SingletonComponent kotlin.collections  Volatile kotlin.collections  asStateFlow kotlin.collections  
BloodSugar kotlin.comparisons  BloodSugarUiState kotlin.comparisons  
Converters kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  MutableStateFlow kotlin.comparisons  SingletonComponent kotlin.comparisons  Volatile kotlin.comparisons  asStateFlow kotlin.comparisons  
BloodSugar 	kotlin.io  BloodSugarUiState 	kotlin.io  
Converters 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  MutableStateFlow 	kotlin.io  SingletonComponent 	kotlin.io  Volatile 	kotlin.io  asStateFlow 	kotlin.io  
BloodSugar 
kotlin.jvm  BloodSugarUiState 
kotlin.jvm  
Converters 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  SingletonComponent 
kotlin.jvm  Volatile 
kotlin.jvm  asStateFlow 
kotlin.jvm  
BloodSugar 
kotlin.ranges  BloodSugarUiState 
kotlin.ranges  
Converters 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  SingletonComponent 
kotlin.ranges  Volatile 
kotlin.ranges  asStateFlow 
kotlin.ranges  KClass kotlin.reflect  
BloodSugar kotlin.sequences  BloodSugarUiState kotlin.sequences  
Converters kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  MutableStateFlow kotlin.sequences  SingletonComponent kotlin.sequences  Volatile kotlin.sequences  asStateFlow kotlin.sequences  
BloodSugar kotlin.text  BloodSugarUiState kotlin.text  
Converters kotlin.text  ExperimentalMaterial3Api kotlin.text  MutableStateFlow kotlin.text  SingletonComponent kotlin.text  Volatile kotlin.text  asStateFlow kotlin.text  launch kotlinx.coroutines  BloodSugarUiState kotlinx.coroutines.flow  Date kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      