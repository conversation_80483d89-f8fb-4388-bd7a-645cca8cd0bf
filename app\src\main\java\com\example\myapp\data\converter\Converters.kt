package com.example.myapp.data.converter

import androidx.room.TypeConverter
import com.example.myapp.data.entity.MeasurementType
import java.util.Date

class Converters {
    
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun fromMeasurementType(type: MeasurementType): String {
        return type.name
    }

    @TypeConverter
    fun toMeasurementType(type: String): MeasurementType {
        return MeasurementType.valueOf(type)
    }
}
