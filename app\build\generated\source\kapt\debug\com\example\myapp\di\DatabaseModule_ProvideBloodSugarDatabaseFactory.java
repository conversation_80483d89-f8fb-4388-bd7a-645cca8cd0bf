package com.example.myapp.di;

import android.content.Context;
import com.example.myapp.data.database.BloodSugarDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideBloodSugarDatabaseFactory implements Factory<BloodSugarDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideBloodSugarDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public BloodSugarDatabase get() {
    return provideBloodSugarDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideBloodSugarDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideBloodSugarDatabaseFactory(contextProvider);
  }

  public static BloodSugarDatabase provideBloodSugarDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideBloodSugarDatabase(context));
  }
}
