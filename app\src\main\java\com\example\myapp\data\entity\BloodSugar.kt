package com.example.myapp.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "blood_sugar")
data class BloodSugar(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val value: Float, // 血糖值 (mmol/L)
    val measurementType: MeasurementType, // 测量类型
    val dateTime: Date, // 测量时间
    val note: String = "", // 备注
    val meterPhotoPath: String = "", // 血糖仪照片路径
    val foodPhotoPath: String = "" // 饮食照片路径
)

enum class MeasurementType(val displayName: String) {
    FASTING("空腹"),
    BEFORE_BREAKFAST("早餐前"),
    AFTER_BREAKFAST("早餐后"),
    BEFORE_LUNCH("午餐前"),
    AFTER_LUNCH("午餐后"),
    BEFORE_DINNER("晚餐前"),
    AFTER_DINNER("晚餐后"),
    BEFORE_SLEEP("睡前"),
    RANDOM("随机")
}
