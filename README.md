# 臭屁屁的血糖记录APP

一个基于Android Jetpack Compose开发的血糖记录管理应用，帮助用户轻松记录和管理血糖数据。专为臭屁屁量身定制！

## 功能特性

### 📊 核心功能
- **血糖记录管理**：添加、编辑、删除血糖记录
- **多种测量类型**：支持空腹、餐前、餐后、睡前等多种测量类型
- **数据可视化**：简洁的趋势图表显示血糖变化
- **统计分析**：自动计算最近30天的平均值、最高值、最低值等统计信息

### 🎨 用户界面
- **Material3设计**：采用最新的Material Design 3设计规范
- **响应式布局**：适配不同屏幕尺寸
- **直观操作**：简单易用的用户界面
- **实时反馈**：血糖值颜色编码（正常/异常）

### 💾 数据管理
- **本地存储**：使用Room数据库安全存储数据
- **数据持久化**：应用关闭后数据不丢失
- **快速检索**：支持按日期范围和测量类型筛选

## 技术架构

### 🛠️ 技术栈
- **开发语言**：Kotlin
- **UI框架**：Jetpack Compose
- **架构模式**：MVVM + Repository
- **数据库**：Room
- **依赖注入**：Hilt
- **异步处理**：Kotlin Coroutines + Flow

### 📱 最低系统要求
- Android 7.0 (API Level 24) 及以上
- 目标SDK：Android 15 (API Level 35)

## 项目结构

```
app/src/main/java/com/example/myapp/
├── data/                          # 数据层
│   ├── entity/                    # 数据实体
│   │   └── BloodSugar.kt         # 血糖记录实体
│   ├── dao/                       # 数据访问对象
│   │   └── BloodSugarDao.kt      # 血糖记录DAO
│   ├── database/                  # 数据库
│   │   └── BloodSugarDatabase.kt # Room数据库
│   ├── repository/                # 仓库层
│   │   └── BloodSugarRepository.kt
│   └── converter/                 # 类型转换器
│       └── Converters.kt
├── di/                           # 依赖注入
│   └── DatabaseModule.kt
├── ui/                           # UI层
│   ├── components/               # UI组件
│   │   ├── BloodSugarItem.kt    # 血糖记录项
│   │   ├── StatisticsCard.kt    # 统计卡片
│   │   ├── BloodSugarChart.kt   # 血糖图表
│   │   ├── AddBloodSugarDialog.kt # 添加记录对话框
│   │   └── EditBloodSugarDialog.kt # 编辑记录对话框
│   ├── screen/                   # 页面
│   │   └── HomeScreen.kt        # 主页面
│   ├── viewmodel/               # 视图模型
│   │   └── BloodSugarViewModel.kt
│   └── theme/                   # 主题
│       ├── Color.kt
│       ├── Theme.kt
│       └── Type.kt
├── BloodSugarApplication.kt     # 应用程序类
└── MainActivity.kt              # 主Activity
```

## 使用说明

### 添加血糖记录
1. 点击右下角的"+"按钮
2. 输入血糖值（mmol/L）
3. 选择测量类型（空腹、餐前、餐后等）
4. 添加备注（可选）
5. 点击"添加"保存记录

### 编辑血糖记录
1. 在记录列表中点击编辑图标
2. 修改血糖值、测量类型或备注
3. 点击"保存"更新记录

### 删除血糖记录
1. 在记录列表中点击删除图标
2. 确认删除操作

### 查看统计信息
- 主页面顶部显示最近30天的统计数据
- 包括平均值、最高值、最低值和记录总数

### 查看趋势图表
- 主页面中部显示最近10条记录的趋势图
- 红色点表示异常血糖值
- 蓝色点表示正常血糖值
- 绿色参考线显示正常血糖范围

## 血糖参考值

- **正常范围**：3.9 - 7.8 mmol/L
- **低血糖**：< 3.9 mmol/L
- **高血糖**：> 7.8 mmol/L

*注：具体的血糖目标值应咨询医生，因个人情况而异*

## 开发环境设置

1. 安装Android Studio
2. 克隆项目到本地
3. 打开项目并等待Gradle同步
4. 运行应用

## 构建说明

```bash
# 构建Debug版本
./gradlew assembleDebug

# 构建Release版本
./gradlew assembleRelease

# 运行测试
./gradlew test
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。

## 免责声明

本应用仅用于记录和管理血糖数据，不能替代专业医疗建议。如有健康问题，请咨询专业医生。
