1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:5:5-65
11-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:5:22-62
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:7:22-77
14
15    <uses-feature
15-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:9:5-11:35
16        android:name="android.hardware.camera"
16-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:10:9-47
17        android:required="true" />
17-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:11:9-32
18
19    <permission
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
24    <!-- <uses-sdk android:minSdkVersion="14"/> -->
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
25-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.INTERNET" />
26-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
26-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
27
28    <application
28-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:13:5-35:19
29        android:name="com.example.myapp.BloodSugarApplication"
29-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:14:9-46
30        android:allowBackup="true"
30-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:15:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:16:9-65
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:17:9-54
35        android:icon="@mipmap/ic_launcher"
35-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:18:9-43
36        android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:19:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:20:9-54
38        android:supportsRtl="true"
38-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:21:9-35
39        android:theme="@style/Theme.Myapp" >
39-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:22:9-43
40        <activity
40-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:24:9-34:20
41            android:name="com.example.myapp.MainActivity"
41-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:25:13-41
42            android:exported="true"
42-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:26:13-36
43            android:label="@string/app_name"
43-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:27:13-45
44            android:theme="@style/Theme.Myapp" >
44-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:28:13-47
45            <intent-filter>
45-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:29:13-33:29
46                <action android:name="android.intent.action.MAIN" />
46-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:30:17-69
46-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:30:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:32:17-77
48-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:32:27-74
49            </intent-filter>
50        </activity>
51
52        <service
52-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
53            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
53-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
54            android:directBootAware="true"
54-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:17:13-43
55            android:exported="false" >
55-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
56            <meta-data
56-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
57                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
57-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
58                android:value="com.google.firebase.components.ComponentRegistrar" />
58-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
59            <meta-data
59-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309df72bd7714900301d5bea8f5eb076\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
60                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
60-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309df72bd7714900301d5bea8f5eb076\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
61                android:value="com.google.firebase.components.ComponentRegistrar" />
61-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309df72bd7714900301d5bea8f5eb076\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
62            <meta-data
62-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:20:13-22:85
63                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
63-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:21:17-120
64                android:value="com.google.firebase.components.ComponentRegistrar" />
64-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:22:17-82
65        </service>
66
67        <provider
67-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:9:9-13:38
68            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
68-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:10:13-78
69            android:authorities="com.example.myapp.mlkitinitprovider"
69-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:11:13-69
70            android:exported="false"
70-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:12:13-37
71            android:initOrder="99" />
71-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:13:13-35
72
73        <activity
73-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
74            android:name="com.google.android.gms.common.api.GoogleApiActivity"
74-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
75            android:exported="false"
75-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
76            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
76-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
77
78        <meta-data
78-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9a76451986ca9c11d04c7076059da51\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
79            android:name="com.google.android.gms.version"
79-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9a76451986ca9c11d04c7076059da51\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
80            android:value="@integer/google_play_services_version" />
80-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9a76451986ca9c11d04c7076059da51\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
81
82        <service
82-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
83            android:name="androidx.camera.core.impl.MetadataHolderService"
83-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
84            android:enabled="false"
84-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
85            android:exported="false" >
85-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
86            <meta-data
86-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
87                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
87-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
88                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
88-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
89        </service>
90
91        <provider
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.example.myapp.androidx-startup"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6811206a6bc8d4cceff94738353b60e2\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6811206a6bc8d4cceff94738353b60e2\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6811206a6bc8d4cceff94738353b60e2\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <service
106-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
107            android:name="androidx.room.MultiInstanceInvalidationService"
107-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
108            android:directBootAware="true"
108-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
109            android:exported="false" />
109-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
110
111        <receiver
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
112            android:name="androidx.profileinstaller.ProfileInstallReceiver"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
113            android:directBootAware="false"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
114            android:enabled="true"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
115            android:exported="true"
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
116            android:permission="android.permission.DUMP" >
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
118                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
119            </intent-filter>
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
121                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
122            </intent-filter>
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
124                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
125            </intent-filter>
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
127                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
128            </intent-filter>
129        </receiver>
130
131        <service
131-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
132            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
132-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
133            android:exported="false" >
133-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
134            <meta-data
134-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
135                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
135-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
136                android:value="cct" />
136-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
137        </service>
138        <service
138-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
139            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
139-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
140            android:exported="false"
140-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
141            android:permission="android.permission.BIND_JOB_SERVICE" >
141-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
142        </service>
143
144        <receiver
144-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
145            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
145-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
146            android:exported="false" />
146-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
147    </application>
148
149</manifest>
