package com.example.myapp.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\u001aB\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\u0015\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0003\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"BloodSugarItem", "", "bloodSugar", "Lcom/example/myapp/data/entity/BloodSugar;", "onEdit", "Lkotlin/Function1;", "onDelete", "modifier", "Landroidx/compose/ui/Modifier;", "getBloodSugarColor", "Landroidx/compose/ui/graphics/Color;", "value", "", "(F)J", "app_debug"})
public final class BloodSugarItemKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void BloodSugarItem(@org.jetbrains.annotations.NotNull()
    com.example.myapp.data.entity.BloodSugar bloodSugar, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.myapp.data.entity.BloodSugar, kotlin.Unit> onEdit, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.myapp.data.entity.BloodSugar, kotlin.Unit> onDelete, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final long getBloodSugarColor(float value) {
        return 0L;
    }
}