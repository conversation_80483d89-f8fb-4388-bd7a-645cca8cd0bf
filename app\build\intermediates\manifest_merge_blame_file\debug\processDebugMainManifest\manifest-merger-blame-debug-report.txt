1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:5:5-27:19
18        android:name="com.example.myapp.BloodSugarApplication"
18-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:6:9-46
19        android:allowBackup="true"
19-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@mipmap/ic_launcher"
25-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:10:9-43
26        android:label="@string/app_name"
26-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:12:9-54
28        android:supportsRtl="true"
28-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:13:9-35
29        android:testOnly="true"
30        android:theme="@style/Theme.Myapp" >
30-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:14:9-43
31        <activity
31-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:16:9-26:20
32            android:name="com.example.myapp.MainActivity"
32-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true"
33-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:18:13-36
34            android:label="@string/app_name"
34-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:19:13-45
35            android:theme="@style/Theme.Myapp" >
35-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:20:13-47
36            <intent-filter>
36-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:22:17-69
37-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:24:17-77
39-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42        <activity
42-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcd30f47c325cb8d9071f37b0173e21\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
43            android:name="androidx.compose.ui.tooling.PreviewActivity"
43-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcd30f47c325cb8d9071f37b0173e21\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
44            android:exported="true" />
44-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcd30f47c325cb8d9071f37b0173e21\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
45
46        <provider
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
47            android:name="androidx.startup.InitializationProvider"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
48            android:authorities="com.example.myapp.androidx-startup"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
49            android:exported="false" >
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
50            <meta-data
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.emoji2.text.EmojiCompatInitializer"
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
52                android:value="androidx.startup" />
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72396274bbd68adfa95fcba28171d492\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
54-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72396274bbd68adfa95fcba28171d492\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
55                android:value="androidx.startup" />
55-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72396274bbd68adfa95fcba28171d492\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
58                android:value="androidx.startup" />
58-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
59        </provider>
60
61        <activity
61-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cca47745cbabc152282711a99fb1aac\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
62            android:name="androidx.activity.ComponentActivity"
62-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cca47745cbabc152282711a99fb1aac\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
63            android:exported="true" />
63-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cca47745cbabc152282711a99fb1aac\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
64
65        <service
65-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
66            android:name="androidx.room.MultiInstanceInvalidationService"
66-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
67            android:directBootAware="true"
67-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
68            android:exported="false" />
68-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
69
70        <receiver
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
71            android:name="androidx.profileinstaller.ProfileInstallReceiver"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
72            android:directBootAware="false"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
73            android:enabled="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
74            android:exported="true"
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
75            android:permission="android.permission.DUMP" >
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
77                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
80                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
83                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
86                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
87            </intent-filter>
88        </receiver>
89    </application>
90
91</manifest>
