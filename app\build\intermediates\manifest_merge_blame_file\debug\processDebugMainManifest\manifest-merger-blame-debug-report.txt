1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:5:5-65
11-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:5:22-62
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:7:5-80
13-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:7:22-77
14
15    <uses-feature
15-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:9:5-11:35
16        android:name="android.hardware.camera"
16-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:10:9-47
17        android:required="true" />
17-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:11:9-32
18
19    <permission
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
24    <!-- <uses-sdk android:minSdkVersion="14"/> -->
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
25-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.INTERNET" />
26-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
26-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
27
28    <application
28-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:13:5-35:19
29        android:name="com.example.myapp.BloodSugarApplication"
29-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:14:9-46
30        android:allowBackup="true"
30-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:15:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0effdc45e183036880fc61940e99b3\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:16:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:17:9-54
36        android:icon="@mipmap/ic_launcher"
36-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:18:9-43
37        android:label="@string/app_name"
37-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:19:9-41
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:20:9-54
39        android:supportsRtl="true"
39-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:21:9-35
40        android:testOnly="true"
41        android:theme="@style/Theme.Myapp" >
41-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:22:9-43
42        <activity
42-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:24:9-34:20
43            android:name="com.example.myapp.MainActivity"
43-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:25:13-41
44            android:exported="true"
44-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:26:13-36
45            android:label="@string/app_name"
45-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:27:13-45
46            android:theme="@style/Theme.Myapp" >
46-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:28:13-47
47            <intent-filter>
47-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:29:13-33:29
48                <action android:name="android.intent.action.MAIN" />
48-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:30:17-69
48-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:30:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:32:17-77
50-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:32:27-74
51            </intent-filter>
52        </activity>
53
54        <service
54-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
55            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
55-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
56            android:directBootAware="true"
56-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:17:13-43
57            android:exported="false" >
57-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
58            <meta-data
58-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
59                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
59-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
60                android:value="com.google.firebase.components.ComponentRegistrar" />
60-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a30617dcef3ea091442509dbad486f56\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
61            <meta-data
61-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309df72bd7714900301d5bea8f5eb076\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
62                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
62-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309df72bd7714900301d5bea8f5eb076\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
63                android:value="com.google.firebase.components.ComponentRegistrar" />
63-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\309df72bd7714900301d5bea8f5eb076\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
64            <meta-data
64-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:20:13-22:85
65                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
65-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:21:17-120
66                android:value="com.google.firebase.components.ComponentRegistrar" />
66-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:22:17-82
67        </service>
68
69        <provider
69-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:9:9-13:38
70            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
70-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:10:13-78
71            android:authorities="com.example.myapp.mlkitinitprovider"
71-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:11:13-69
72            android:exported="false"
72-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:12:13-37
73            android:initOrder="99" />
73-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a904817282c6e6ca21191ed7934ac56\transformed\common-18.8.0\AndroidManifest.xml:13:13-35
74
75        <activity
75-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
76            android:name="com.google.android.gms.common.api.GoogleApiActivity"
76-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
77            android:exported="false"
77-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
78            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
78-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc133dde975a2a52fdd6f2f6c3ad94d\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
79
80        <meta-data
80-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9a76451986ca9c11d04c7076059da51\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
81            android:name="com.google.android.gms.version"
81-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9a76451986ca9c11d04c7076059da51\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
82            android:value="@integer/google_play_services_version" />
82-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9a76451986ca9c11d04c7076059da51\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
83
84        <service
84-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
85            android:name="androidx.camera.core.impl.MetadataHolderService"
85-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
86            android:enabled="false"
86-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
87            android:exported="false" >
87-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
89                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
89-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
90                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
90-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad5627010e180aa3692d8744b80d7ae4\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
91        </service>
92
93        <activity
93-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffb8f6b0362b69df25b1cc8fa743a550\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
94            android:name="androidx.compose.ui.tooling.PreviewActivity"
94-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffb8f6b0362b69df25b1cc8fa743a550\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
95            android:exported="true" />
95-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffb8f6b0362b69df25b1cc8fa743a550\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
96
97        <provider
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
98            android:name="androidx.startup.InitializationProvider"
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
99            android:authorities="com.example.myapp.androidx-startup"
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
100            android:exported="false" >
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
101            <meta-data
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.emoji2.text.EmojiCompatInitializer"
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
103                android:value="androidx.startup" />
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d7b3ea9921604a6f9a0b821a9c97a8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6811206a6bc8d4cceff94738353b60e2\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6811206a6bc8d4cceff94738353b60e2\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6811206a6bc8d4cceff94738353b60e2\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
110        </provider>
111
112        <activity
112-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8205359ac3355bebff0c9ef29488efcd\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
113            android:name="androidx.activity.ComponentActivity"
113-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8205359ac3355bebff0c9ef29488efcd\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
114            android:exported="true" />
114-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8205359ac3355bebff0c9ef29488efcd\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
115
116        <service
116-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
117            android:name="androidx.room.MultiInstanceInvalidationService"
117-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
118            android:directBootAware="true"
118-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
119            android:exported="false" />
119-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5bb16ca743d6c8789381d90ec110e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c60eb8809c0b5990630b8c3fc4598dd\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140
141        <service
141-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
142            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
142-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
143            android:exported="false" >
143-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
144            <meta-data
144-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
145                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
145-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
146                android:value="cct" />
146-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\336e950796cc6179aabe681e32297151\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
147        </service>
148        <service
148-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
149            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
149-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
150            android:exported="false"
150-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
151            android:permission="android.permission.BIND_JOB_SERVICE" >
151-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
152        </service>
153
154        <receiver
154-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
155            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
155-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
156            android:exported="false" />
156-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48e2818d0026f990722fdeabbeec4992\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
157    </application>
158
159</manifest>
