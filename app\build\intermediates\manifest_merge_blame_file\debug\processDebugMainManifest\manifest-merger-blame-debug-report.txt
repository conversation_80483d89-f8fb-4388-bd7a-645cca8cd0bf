1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:5:5-27:19
18        android:name="com.example.myapp.BloodSugarApplication"
18-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:6:9-46
19        android:allowBackup="true"
19-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb9e3e29d5dc7261cbb30e01775346f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@mipmap/ic_launcher"
25-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:10:9-43
26        android:label="@string/app_name"
26-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:12:9-54
28        android:supportsRtl="true"
28-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:13:9-35
29        android:theme="@style/Theme.Myapp" >
29-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:14:9-43
30        <activity
30-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:16:9-26:20
31            android:name="com.example.myapp.MainActivity"
31-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:17:13-41
32            android:exported="true"
32-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:18:13-36
33            android:label="@string/app_name"
33-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:19:13-45
34            android:theme="@style/Theme.Myapp" >
34-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:20:13-47
35            <intent-filter>
35-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:21:13-25:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:22:17-69
36-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:22:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:24:17-77
38-->C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\AndroidManifest.xml:24:27-74
39            </intent-filter>
40        </activity>
41        <activity
41-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcd30f47c325cb8d9071f37b0173e21\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
42            android:name="androidx.compose.ui.tooling.PreviewActivity"
42-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcd30f47c325cb8d9071f37b0173e21\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
43            android:exported="true" />
43-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bcd30f47c325cb8d9071f37b0173e21\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
44
45        <provider
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.example.myapp.androidx-startup"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a837b94a138e66dbd6a0501c39cb9e9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72396274bbd68adfa95fcba28171d492\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72396274bbd68adfa95fcba28171d492\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72396274bbd68adfa95fcba28171d492\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <activity
60-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cca47745cbabc152282711a99fb1aac\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
61            android:name="androidx.activity.ComponentActivity"
61-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cca47745cbabc152282711a99fb1aac\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
62            android:exported="true" />
62-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cca47745cbabc152282711a99fb1aac\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
63
64        <service
64-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
65            android:name="androidx.room.MultiInstanceInvalidationService"
65-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
66            android:directBootAware="true"
66-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
67            android:exported="false" />
67-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73e16ec92a06fb810d67be33ada81d82\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07a59d2a29f4f651d06a3fd456c772c3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
