package com.example.myapp.di;

import com.example.myapp.data.dao.BloodSugarDao;
import com.example.myapp.data.database.BloodSugarDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideBloodSugarDaoFactory implements Factory<BloodSugarDao> {
  private final Provider<BloodSugarDatabase> databaseProvider;

  public DatabaseModule_ProvideBloodSugarDaoFactory(Provider<BloodSugarDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public BloodSugarDao get() {
    return provideBloodSugarDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideBloodSugarDaoFactory create(
      Provider<BloodSugarDatabase> databaseProvider) {
    return new DatabaseModule_ProvideBloodSugarDaoFactory(databaseProvider);
  }

  public static BloodSugarDao provideBloodSugarDao(BloodSugarDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideBloodSugarDao(database));
  }
}
