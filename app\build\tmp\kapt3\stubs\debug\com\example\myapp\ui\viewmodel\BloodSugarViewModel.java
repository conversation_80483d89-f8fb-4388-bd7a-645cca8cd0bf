package com.example.myapp.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J&\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\rJ\u000e\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0019J\b\u0010\u001a\u001a\u00020\rH\u0002J\b\u0010\u001b\u001a\u00020\rH\u0002J\u000e\u0010\u001c\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0019R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u001d"}, d2 = {"Lcom/example/myapp/ui/viewmodel/BloodSugarViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/example/myapp/data/repository/BloodSugarRepository;", "(Lcom/example/myapp/data/repository/BloodSugarRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/myapp/ui/viewmodel/BloodSugarUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "addBloodSugar", "", "value", "", "type", "Lcom/example/myapp/data/entity/MeasurementType;", "dateTime", "Ljava/util/Date;", "note", "", "clearError", "deleteBloodSugar", "bloodSugar", "Lcom/example/myapp/data/entity/BloodSugar;", "loadBloodSugars", "loadStatistics", "updateBloodSugar", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class BloodSugarViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.myapp.data.repository.BloodSugarRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.myapp.ui.viewmodel.BloodSugarUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.myapp.ui.viewmodel.BloodSugarUiState> uiState = null;
    
    @javax.inject.Inject()
    public BloodSugarViewModel(@org.jetbrains.annotations.NotNull()
    com.example.myapp.data.repository.BloodSugarRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.myapp.ui.viewmodel.BloodSugarUiState> getUiState() {
        return null;
    }
    
    private final void loadBloodSugars() {
    }
    
    private final void loadStatistics() {
    }
    
    public final void addBloodSugar(float value, @org.jetbrains.annotations.NotNull()
    com.example.myapp.data.entity.MeasurementType type, @org.jetbrains.annotations.NotNull()
    java.util.Date dateTime, @org.jetbrains.annotations.NotNull()
    java.lang.String note) {
    }
    
    public final void updateBloodSugar(@org.jetbrains.annotations.NotNull()
    com.example.myapp.data.entity.BloodSugar bloodSugar) {
    }
    
    public final void deleteBloodSugar(@org.jetbrains.annotations.NotNull()
    com.example.myapp.data.entity.BloodSugar bloodSugar) {
    }
    
    public final void clearError() {
    }
}