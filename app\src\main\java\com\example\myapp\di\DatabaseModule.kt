package com.example.myapp.di

import android.content.Context
import androidx.room.Room
import com.example.myapp.data.dao.BloodSugarDao
import com.example.myapp.data.database.BloodSugarDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideBloodSugarDatabase(@ApplicationContext context: Context): BloodSugarDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            BloodSugarDatabase::class.java,
            "blood_sugar_database"
        ).build()
    }
    
    @Provides
    fun provideBloodSugarDao(database: BloodSugarDatabase): BloodSugarDao {
        return database.bloodSugarDao()
    }
}
