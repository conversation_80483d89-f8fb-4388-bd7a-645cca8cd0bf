package com.example.myapp.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TextRecognitionService_Factory implements Factory<TextRecognitionService> {
  @Override
  public TextRecognitionService get() {
    return newInstance();
  }

  public static TextRecognitionService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TextRecognitionService newInstance() {
    return new TextRecognitionService();
  }

  private static final class InstanceHolder {
    private static final TextRecognitionService_Factory INSTANCE = new TextRecognitionService_Factory();
  }
}
