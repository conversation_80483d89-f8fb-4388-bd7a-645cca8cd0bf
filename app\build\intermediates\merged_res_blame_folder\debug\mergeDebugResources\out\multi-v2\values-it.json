{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-57:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c1b5f2840ec1b7ea00a29b86e3ab0de\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8759,8857", "endColumns": "97,98", "endOffsets": "8852,8951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3dbf5ba78cbeee9020b93a3c65c010c\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4782,4868,4971,5051,5134,5233,5339,5439,5540,5628,5738,5838,5943,6061,6141,6255", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4777,4863,4966,5046,5129,5228,5334,5434,5535,5623,5733,5833,5938,6056,6136,6250,6357"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1509,1636,1760,1882,2006,2111,2207,2320,2463,2582,2740,2824,2936,3030,3130,3249,3371,3488,3630,3770,3913,4089,4224,4344,4467,4597,4692,4789,4916,5054,5154,5264,5370,5513,5661,5771,5872,5961,6057,6150,6236,6322,6425,6505,6588,6687,6793,6893,6994,7082,7192,7292,7397,7515,7595,7709", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "1631,1755,1877,2001,2106,2202,2315,2458,2577,2735,2819,2931,3025,3125,3244,3366,3483,3625,3765,3908,4084,4219,4339,4462,4592,4687,4784,4911,5049,5149,5259,5365,5508,5656,5766,5867,5956,6052,6145,6231,6317,6420,6500,6583,6682,6788,6888,6989,7077,7187,7287,7392,7510,7590,7704,7811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\74a2a50a3cf75b402410cb079508727a\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1015,1079,1163,1251,1336,1414,1483", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1010,1074,1158,1246,1331,1409,1478,1599"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,952,1039,1137,1237,1324,1403,7816,7909,8004,8069,8133,8217,8305,8491,8569,8638", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "947,1034,1132,1232,1319,1398,1504,7904,7999,8064,8128,8212,8300,8385,8564,8633,8754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1199c7ee149e3eccc471d61f7d603781\\transformed\\core-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,615,722,8390", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "198,300,399,501,610,717,847,8486"}}]}]}