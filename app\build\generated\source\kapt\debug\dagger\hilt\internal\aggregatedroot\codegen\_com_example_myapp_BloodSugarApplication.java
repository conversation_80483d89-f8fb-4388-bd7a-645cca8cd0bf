package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.myapp.BloodSugarApplication",
    rootPackage = "com.example.myapp",
    originatingRoot = "com.example.myapp.BloodSugarApplication",
    originatingRootPackage = "com.example.myapp",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "BloodSugarApplication",
    originatingRootSimpleNames = "BloodSugarApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_myapp_BloodSugarApplication {
}
