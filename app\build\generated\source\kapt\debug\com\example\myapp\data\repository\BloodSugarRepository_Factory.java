package com.example.myapp.data.repository;

import com.example.myapp.data.dao.BloodSugarDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BloodSugarRepository_Factory implements Factory<BloodSugarRepository> {
  private final Provider<BloodSugarDao> bloodSugarDaoProvider;

  public BloodSugarRepository_Factory(Provider<BloodSugarDao> bloodSugarDaoProvider) {
    this.bloodSugarDaoProvider = bloodSugarDaoProvider;
  }

  @Override
  public BloodSugarRepository get() {
    return newInstance(bloodSugarDaoProvider.get());
  }

  public static BloodSugarRepository_Factory create(Provider<BloodSugarDao> bloodSugarDaoProvider) {
    return new BloodSugarRepository_Factory(bloodSugarDaoProvider);
  }

  public static BloodSugarRepository newInstance(BloodSugarDao bloodSugarDao) {
    return new BloodSugarRepository(bloodSugarDao);
  }
}
