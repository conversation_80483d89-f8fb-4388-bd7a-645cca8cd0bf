package com.example.myapp.ui.viewmodel;

import com.example.myapp.data.repository.BloodSugarRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BloodSugarViewModel_Factory implements Factory<BloodSugarViewModel> {
  private final Provider<BloodSugarRepository> repositoryProvider;

  public BloodSugarViewModel_Factory(Provider<BloodSugarRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public BloodSugarViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static BloodSugarViewModel_Factory create(
      Provider<BloodSugarRepository> repositoryProvider) {
    return new BloodSugarViewModel_Factory(repositoryProvider);
  }

  public static BloodSugarViewModel newInstance(BloodSugarRepository repository) {
    return new BloodSugarViewModel(repository);
  }
}
