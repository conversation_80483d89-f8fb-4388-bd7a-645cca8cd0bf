/ Header Record For PersistentHashMapValueStorage= <app/src/main/java/com/example/myapp/BloodSugarApplication.kt4 3app/src/main/java/com/example/myapp/MainActivity.ktA @app/src/main/java/com/example/myapp/data/converter/Converters.kt> =app/src/main/java/com/example/myapp/data/dao/BloodSugarDao.ktH Gapp/src/main/java/com/example/myapp/data/database/BloodSugarDatabase.kt> =app/src/main/java/com/example/myapp/data/entity/BloodSugar.ktL Kapp/src/main/java/com/example/myapp/data/repository/BloodSugarRepository.kt9 8app/src/main/java/com/example/myapp/di/DatabaseModule.ktF Eapp/src/main/java/com/example/myapp/service/TextRecognitionService.ktI Happ/src/main/java/com/example/myapp/ui/components/AddBloodSugarDialog.ktE Dapp/src/main/java/com/example/myapp/ui/components/BloodSugarChart.ktD Capp/src/main/java/com/example/myapp/ui/components/BloodSugarItem.ktC Bapp/src/main/java/com/example/myapp/ui/components/CameraCapture.ktJ Iapp/src/main/java/com/example/myapp/ui/components/EditBloodSugarDialog.ktB Aapp/src/main/java/com/example/myapp/ui/components/PhotoPreview.ktD Capp/src/main/java/com/example/myapp/ui/components/StatisticsCard.kt< ;app/src/main/java/com/example/myapp/ui/screen/HomeScreen.kt6 5app/src/main/java/com/example/myapp/ui/theme/Color.kt6 5app/src/main/java/com/example/myapp/ui/theme/Theme.kt5 4app/src/main/java/com/example/myapp/ui/theme/Type.ktH Gapp/src/main/java/com/example/myapp/ui/viewmodel/BloodSugarViewModel.kt