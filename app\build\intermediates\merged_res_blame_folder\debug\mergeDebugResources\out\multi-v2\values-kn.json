{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-68:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abc133dde975a2a52fdd6f2f6c3ad94d\\transformed\\play-services-base-18.1.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3785,3896,4066,4199,4314,4457,4586,4694,4939,5089,5202,5367,5502,5647,5804,5873,5936", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "3891,4061,4194,4309,4452,4581,4689,4788,5084,5197,5362,5497,5642,5799,5868,5931,6016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e00bb0cb13b50e7a03e9d56b3ab5729\\transformed\\material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4765,4854,4965,5045,5129,5229,5337,5437,5538,5625,5738,5840,5945,6066,6146,6256", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4760,4849,4960,5040,5124,5224,5332,5432,5533,5620,5733,5835,5940,6061,6141,6251,6348"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6483,6607,6731,6853,6976,7075,7173,7288,7445,7575,7727,7813,7919,8015,8117,8233,8366,8477,8616,8751,8884,9062,9186,9304,9425,9552,9649,9746,9868,10006,10112,10221,10327,10466,10611,10721,10830,10906,11006,11106,11193,11282,11393,11473,11557,11657,11765,11865,11966,12053,12166,12268,12373,12494,12574,12684", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "6602,6726,6848,6971,7070,7168,7283,7440,7570,7722,7808,7914,8010,8112,8228,8361,8472,8611,8746,8879,9057,9181,9299,9420,9547,9644,9741,9863,10001,10107,10216,10322,10461,10606,10716,10825,10901,11001,11101,11188,11277,11388,11468,11552,11652,11760,11860,11961,12048,12161,12263,12368,12489,12569,12679,12776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0233180bfb2a73e0304a16e4c911a11f\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "36,37,56,57,58,59,60,117,118,119,120,121,122,124,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3604,3701,6021,6117,6217,6306,6390,12781,12872,12957,13028,13099,13181,13350,13530,13607,13676", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "3696,3780,6112,6212,6301,6385,6478,12867,12952,13023,13094,13176,13262,13424,13602,13671,13789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a76451986ca9c11d04c7076059da51\\transformed\\play-services-basement-18.1.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4793", "endColumns": "145", "endOffsets": "4934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22f038d89e49e38792143e9fe11d5050\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,13267", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,13345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d0effdc45e183036880fc61940e99b3\\transformed\\core-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "29,30,31,32,33,34,35,125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2859,2957,3060,3161,3267,3368,3476,13429", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "2952,3055,3156,3262,3363,3471,3599,13525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86b2aff77130131bb6ebfada36b55526\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "129,130", "startColumns": "4,4", "startOffsets": "13794,13882", "endColumns": "87,94", "endOffsets": "13877,13972"}}]}]}