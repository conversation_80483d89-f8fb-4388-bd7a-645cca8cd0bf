{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-68:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abc133dde975a2a52fdd6f2f6c3ad94d\\transformed\\play-services-base-18.1.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3517,3621,3760,3876,3978,4093,4210,4317,4533,4678,4781,4917,5035,5153,5272,5331,5389", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "3616,3755,3871,3973,4088,4205,4312,4407,4673,4776,4912,5030,5148,5267,5326,5384,5459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86b2aff77130131bb6ebfada36b55526\\transformed\\foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,78", "endOffsets": "132,211"}, "to": {"startLines": "129,130", "startColumns": "4,4", "startOffsets": "12364,12446", "endColumns": "81,78", "endOffsets": "12441,12520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d0effdc45e183036880fc61940e99b3\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "29,30,31,32,33,34,35,125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2685,2777,2877,2971,3068,3164,3262,12011", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "2772,2872,2966,3063,3159,3257,3357,12107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a76451986ca9c11d04c7076059da51\\transformed\\play-services-basement-18.1.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4412", "endColumns": "120", "endOffsets": "4528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0233180bfb2a73e0304a16e4c911a11f\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,894,958,1031,1107,1175,1249,1313", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,889,953,1026,1102,1170,1244,1308,1422"}, "to": {"startLines": "36,37,56,57,58,59,60,117,118,119,120,121,122,124,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3362,3441,5464,5552,5642,5722,5797,11429,11508,11587,11651,11715,11788,11943,12112,12186,12250", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "3436,3512,5547,5637,5717,5792,5871,11503,11582,11646,11710,11783,11859,12006,12181,12245,12359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22f038d89e49e38792143e9fe11d5050\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,11864", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,11938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e00bb0cb13b50e7a03e9d56b3ab5729\\transformed\\material3-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,264,370,474,566,655,761,880,990,1112,1194,1291,1376,1466,1575,1689,1791,1904,2015,2127,2260,2369,2473,2580,2689,2775,2870,2979,3088,3179,3277,3374,3488,3607,3706,3798,3872,3961,4049,4132,4214,4309,4389,4471,4568,4663,4758,4855,4938,5034,5128,5226,5343,5423,5517", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "155,259,365,469,561,650,756,875,985,1107,1189,1286,1371,1461,1570,1684,1786,1899,2010,2122,2255,2364,2468,2575,2684,2770,2865,2974,3083,3174,3272,3369,3483,3602,3701,3793,3867,3956,4044,4127,4209,4304,4384,4466,4563,4658,4753,4850,4933,5029,5123,5221,5338,5418,5512,5603"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5876,5981,6085,6191,6295,6387,6476,6582,6701,6811,6933,7015,7112,7197,7287,7396,7510,7612,7725,7836,7948,8081,8190,8294,8401,8510,8596,8691,8800,8909,9000,9098,9195,9309,9428,9527,9619,9693,9782,9870,9953,10035,10130,10210,10292,10389,10484,10579,10676,10759,10855,10949,11047,11164,11244,11338", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "5976,6080,6186,6290,6382,6471,6577,6696,6806,6928,7010,7107,7192,7282,7391,7505,7607,7720,7831,7943,8076,8185,8289,8396,8505,8591,8686,8795,8904,8995,9093,9190,9304,9423,9522,9614,9688,9777,9865,9948,10030,10125,10205,10287,10384,10479,10574,10671,10754,10850,10944,11042,11159,11239,11333,11424"}}]}]}