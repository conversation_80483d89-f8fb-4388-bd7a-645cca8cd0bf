{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-57:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1199c7ee149e3eccc471d61f7d603781\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,501,615,720,8186", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "197,299,397,496,610,715,837,8282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\74a2a50a3cf75b402410cb079508727a\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,990,1060,1143,1230,1302,1384,1452", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,985,1055,1138,1225,1297,1379,1447,1567"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1022,1131,1236,1313,1390,7630,7720,7803,7874,7944,8027,8114,8287,8369,8437", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "932,1017,1126,1231,1308,1385,1478,7715,7798,7869,7939,8022,8109,8181,8364,8432,8552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c1b5f2840ec1b7ea00a29b86e3ab0de\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8557,8645", "endColumns": "87,87", "endOffsets": "8640,8728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3dbf5ba78cbeee9020b93a3c65c010c\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4633,4720,4832,4912,4999,5094,5199,5290,5399,5487,5593,5694,5804,5922,6002,6105", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4628,4715,4827,4907,4994,5089,5194,5285,5394,5482,5588,5689,5799,5917,5997,6100,6197"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1483,1598,1715,1837,1952,2052,2151,2267,2405,2527,2669,2753,2852,2944,3040,3157,3281,3385,3525,3661,3805,3966,4098,4219,4344,4465,4558,4658,4778,4902,5001,5105,5211,5352,5499,5610,5709,5783,5878,5974,6061,6148,6260,6340,6427,6522,6627,6718,6827,6915,7021,7122,7232,7350,7430,7533", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "1593,1710,1832,1947,2047,2146,2262,2400,2522,2664,2748,2847,2939,3035,3152,3276,3380,3520,3656,3800,3961,4093,4214,4339,4460,4553,4653,4773,4897,4996,5100,5206,5347,5494,5605,5704,5778,5873,5969,6056,6143,6255,6335,6422,6517,6622,6713,6822,6910,7016,7117,7227,7345,7425,7528,7625"}}]}]}