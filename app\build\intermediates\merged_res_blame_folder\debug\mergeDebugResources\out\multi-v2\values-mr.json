{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-68:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e00bb0cb13b50e7a03e9d56b3ab5729\\transformed\\material3-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4665,4755,4855,4935,5018,5117,5216,5313,5412,5499,5603,5703,5807,5925,6005,6105", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4660,4750,4850,4930,5013,5112,5211,5308,5407,5494,5598,5698,5802,5920,6000,6100,6194"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6315,6440,6563,6674,6799,6902,7002,7117,7253,7376,7522,7607,7713,7804,7902,8016,8146,8257,8392,8526,8654,8832,8957,9073,9192,9317,9409,9504,9624,9753,9853,9956,10065,10202,10344,10459,10557,10633,10736,10840,10925,11015,11115,11195,11278,11377,11476,11573,11672,11759,11863,11963,12067,12185,12265,12365", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "6435,6558,6669,6794,6897,6997,7112,7248,7371,7517,7602,7708,7799,7897,8011,8141,8252,8387,8521,8649,8827,8952,9068,9187,9312,9404,9499,9619,9748,9848,9951,10060,10197,10339,10454,10552,10628,10731,10835,10920,11010,11110,11190,11273,11372,11471,11568,11667,11754,11858,11958,12062,12180,12260,12360,12454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22f038d89e49e38792143e9fe11d5050\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,12935", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,13010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0233180bfb2a73e0304a16e4c911a11f\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "36,37,56,57,58,59,60,117,118,119,120,121,122,124,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3527,3621,5862,5959,6057,6144,6230,12459,12548,12631,12700,12770,12850,13015,13187,13263,13329", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "3616,3698,5954,6052,6139,6225,6310,12543,12626,12695,12765,12845,12930,13081,13258,13324,13442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abc133dde975a2a52fdd6f2f6c3ad94d\\transformed\\play-services-base-18.1.0\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3703,3810,3978,4101,4213,4358,4479,4587,4827,4977,5085,5239,5363,5502,5655,5715,5781", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "3805,3973,4096,4208,4353,4474,4582,4679,4972,5080,5234,5358,5497,5650,5710,5776,5857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86b2aff77130131bb6ebfada36b55526\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "129,130", "startColumns": "4,4", "startOffsets": "13447,13532", "endColumns": "84,84", "endOffsets": "13527,13612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d0effdc45e183036880fc61940e99b3\\transformed\\core-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2795,2895,2999,3100,3203,3305,3410,13086", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "2890,2994,3095,3198,3300,3405,3522,13182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a76451986ca9c11d04c7076059da51\\transformed\\play-services-basement-18.1.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4684", "endColumns": "142", "endOffsets": "4822"}}]}]}