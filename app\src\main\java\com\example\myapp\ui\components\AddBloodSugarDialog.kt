package com.example.myapp.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.example.myapp.data.entity.MeasurementType
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddBloodSugarDialog(
    onDismiss: () -> Unit,
    onConfirm: (Float, MeasurementType, Date, String) -> Unit,
    modifier: Modifier = Modifier
) {
    var bloodSugarValue by remember { mutableStateOf("") }
    var selectedType by remember { mutableStateOf(MeasurementType.RANDOM) }
    var note by remember { mutableStateOf("") }
    var expanded by remember { mutableStateOf(false) }
    var isError by remember { mutableStateOf(false) }
    
    val currentDateTime = remember { Date() }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加血糖记录") },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 血糖值输入
                OutlinedTextField(
                    value = bloodSugarValue,
                    onValueChange = { 
                        bloodSugarValue = it
                        isError = false
                    },
                    label = { Text("血糖值 (mmol/L)") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    isError = isError,
                    supportingText = if (isError) {
                        { Text("请输入有效的血糖值") }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 测量类型选择
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = selectedType.displayName,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("测量类型") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        MeasurementType.entries.forEach { type ->
                            DropdownMenuItem(
                                text = { Text(type.displayName) },
                                onClick = {
                                    selectedType = type
                                    expanded = false
                                }
                            )
                        }
                    }
                }
                
                // 时间显示
                OutlinedTextField(
                    value = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                        .format(currentDateTime),
                    onValueChange = {},
                    label = { Text("测量时间") },
                    readOnly = true,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 备注输入
                OutlinedTextField(
                    value = note,
                    onValueChange = { note = it },
                    label = { Text("备注（可选）") },
                    maxLines = 3,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val value = bloodSugarValue.toFloatOrNull()
                    if (value != null && value > 0) {
                        onConfirm(value, selectedType, currentDateTime, note)
                    } else {
                        isError = true
                    }
                }
            ) {
                Text("添加")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
