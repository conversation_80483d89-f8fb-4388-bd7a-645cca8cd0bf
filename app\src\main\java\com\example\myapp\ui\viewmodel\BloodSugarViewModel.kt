package com.example.myapp.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapp.data.entity.BloodSugar
import com.example.myapp.data.entity.MeasurementType
import com.example.myapp.data.repository.BloodSugarRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

data class BloodSugarUiState(
    val bloodSugars: List<BloodSugar> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val statistics: Statistics = Statistics()
)

data class Statistics(
    val average: Float = 0f,
    val max: Float = 0f,
    val min: Float = 0f,
    val count: Int = 0
)

@HiltViewModel
class BloodSugarViewModel @Inject constructor(
    private val repository: BloodSugarRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(BloodSugarUiState())
    val uiState: StateFlow<BloodSugarUiState> = _uiState.asStateFlow()
    
    init {
        loadBloodSugars()
        loadStatistics()
    }
    
    private fun loadBloodSugars() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            try {
                repository.getAllBloodSugars().collect { bloodSugars ->
                    _uiState.update { 
                        it.copy(
                            bloodSugars = bloodSugars,
                            isLoading = false,
                            errorMessage = null
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        errorMessage = e.message
                    )
                }
            }
        }
    }
    
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val calendar = Calendar.getInstance()
                val endDate = calendar.time
                calendar.add(Calendar.DAY_OF_YEAR, -30) // 最近30天
                val startDate = calendar.time
                
                val average = repository.getAverageBloodSugar(startDate, endDate) ?: 0f
                val max = repository.getMaxBloodSugar(startDate, endDate) ?: 0f
                val min = repository.getMinBloodSugar(startDate, endDate) ?: 0f
                val count = repository.getRecordCount(startDate, endDate)
                
                _uiState.update { 
                    it.copy(
                        statistics = Statistics(average, max, min, count)
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = e.message)
                }
            }
        }
    }
    
    fun addBloodSugar(value: Float, type: MeasurementType, dateTime: Date, note: String) {
        viewModelScope.launch {
            try {
                val bloodSugar = BloodSugar(
                    value = value,
                    measurementType = type,
                    dateTime = dateTime,
                    note = note
                )
                repository.insertBloodSugar(bloodSugar)
                loadStatistics() // 重新加载统计数据
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = e.message)
                }
            }
        }
    }
    
    fun updateBloodSugar(bloodSugar: BloodSugar) {
        viewModelScope.launch {
            try {
                repository.updateBloodSugar(bloodSugar)
                loadStatistics()
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = e.message)
                }
            }
        }
    }
    
    fun deleteBloodSugar(bloodSugar: BloodSugar) {
        viewModelScope.launch {
            try {
                repository.deleteBloodSugar(bloodSugar)
                loadStatistics()
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = e.message)
                }
            }
        }
    }
    
    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
}
