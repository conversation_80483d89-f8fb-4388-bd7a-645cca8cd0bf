{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-57:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36cdb26bea2ca7fcb7382d8ae39b5662\\transformed\\foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,81", "endOffsets": "135,217"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "7830,7915", "endColumns": "84,81", "endOffsets": "7910,7992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdb9e3e29d5dc7261cbb30e01775346f\\transformed\\core-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,297,391,487,580,673,7471", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "192,292,386,482,575,668,769,7567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\195d9e3dd5a9d6ba896447ff14efbb05\\transformed\\material3-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,273,382,488,581,671,778,892,1000,1124,1206,1303,1388,1478,1585,1698,1800,1924,2046,2160,2287,2397,2498,2602,2710,2796,2891,2999,3111,3202,3299,3396,3517,3643,3742,3834,3909,4002,4094,4177,4260,4357,4437,4519,4617,4712,4805,4902,4985,5081,5176,5274,5385,5465,5562", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "161,268,377,483,576,666,773,887,995,1119,1201,1298,1383,1473,1580,1693,1795,1919,2041,2155,2282,2392,2493,2597,2705,2791,2886,2994,3106,3197,3294,3391,3512,3638,3737,3829,3904,3997,4089,4172,4255,4352,4432,4514,4612,4707,4800,4897,4980,5076,5171,5269,5380,5460,5557,5651"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1363,1474,1581,1690,1796,1889,1979,2086,2200,2308,2432,2514,2611,2696,2786,2893,3006,3108,3232,3354,3468,3595,3705,3806,3910,4018,4104,4199,4307,4419,4510,4607,4704,4825,4951,5050,5142,5217,5310,5402,5485,5568,5665,5745,5827,5925,6020,6113,6210,6293,6389,6484,6582,6693,6773,6870", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "1469,1576,1685,1791,1884,1974,2081,2195,2303,2427,2509,2606,2691,2781,2888,3001,3103,3227,3349,3463,3590,3700,3801,3905,4013,4099,4194,4302,4414,4505,4602,4699,4820,4946,5045,5137,5212,5305,5397,5480,5563,5660,5740,5822,5920,6015,6108,6205,6288,6384,6479,6577,6688,6768,6865,6959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f0b3c638ab77a0ce9d8c56647651228\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,860,938,1027,1124,1207,1285,6964,7049,7124,7188,7252,7326,7402,7572,7648,7713", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "855,933,1022,1119,1202,1280,1358,7044,7119,7183,7247,7321,7397,7466,7643,7708,7825"}}]}]}