{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-68:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e00bb0cb13b50e7a03e9d56b3ab5729\\transformed\\material3-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,273,382,488,581,671,778,892,1000,1124,1206,1303,1388,1478,1585,1698,1800,1924,2046,2160,2287,2397,2498,2602,2710,2796,2891,2999,3111,3202,3299,3396,3517,3643,3742,3834,3909,4002,4094,4177,4260,4357,4437,4519,4617,4712,4805,4902,4985,5081,5176,5274,5385,5465,5562", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "161,268,377,483,576,666,773,887,995,1119,1201,1298,1383,1473,1580,1693,1795,1919,2041,2155,2282,2392,2493,2597,2705,2791,2886,2994,3106,3197,3294,3391,3512,3638,3737,3829,3904,3997,4089,4172,4255,4352,4432,4514,4612,4707,4800,4897,4980,5076,5171,5269,5380,5460,5557,5651"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5908,6019,6126,6235,6341,6434,6524,6631,6745,6853,6977,7059,7156,7241,7331,7438,7551,7653,7777,7899,8013,8140,8250,8351,8455,8563,8649,8744,8852,8964,9055,9152,9249,9370,9496,9595,9687,9762,9855,9947,10030,10113,10210,10290,10372,10470,10565,10658,10755,10838,10934,11029,11127,11238,11318,11415", "endColumns": "110,106,108,105,92,89,106,113,107,123,81,96,84,89,106,112,101,123,121,113,126,109,100,103,107,85,94,107,111,90,96,96,120,125,98,91,74,92,91,82,82,96,79,81,97,94,92,96,82,95,94,97,110,79,96,93", "endOffsets": "6014,6121,6230,6336,6429,6519,6626,6740,6848,6972,7054,7151,7236,7326,7433,7546,7648,7772,7894,8008,8135,8245,8346,8450,8558,8644,8739,8847,8959,9050,9147,9244,9365,9491,9590,9682,9757,9850,9942,10025,10108,10205,10285,10367,10465,10560,10653,10750,10833,10929,11024,11122,11233,11313,11410,11504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0233180bfb2a73e0304a16e4c911a11f\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "36,37,56,57,58,59,60,117,118,119,120,121,122,124,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3360,3446,5483,5572,5669,5752,5830,11509,11594,11669,11733,11797,11871,12026,12196,12272,12337", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "3441,3519,5567,5664,5747,5825,5903,11589,11664,11728,11792,11866,11942,12090,12267,12332,12449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d0effdc45e183036880fc61940e99b3\\transformed\\core-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "29,30,31,32,33,34,35,125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2691,2783,2883,2977,3073,3166,3259,12095", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "2778,2878,2972,3068,3161,3254,3355,12191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a76451986ca9c11d04c7076059da51\\transformed\\play-services-basement-18.1.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4444", "endColumns": "121", "endOffsets": "4561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86b2aff77130131bb6ebfada36b55526\\transformed\\foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,81", "endOffsets": "135,217"}, "to": {"startLines": "129,130", "startColumns": "4,4", "startOffsets": "12454,12539", "endColumns": "84,81", "endOffsets": "12534,12616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22f038d89e49e38792143e9fe11d5050\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,11947", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,12021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abc133dde975a2a52fdd6f2f6c3ad94d\\transformed\\play-services-base-18.1.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3524,3628,3762,3882,3988,4120,4240,4345,4566,4700,4801,4934,5053,5173,5293,5353,5412", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "3623,3757,3877,3983,4115,4235,4340,4439,4695,4796,4929,5048,5168,5288,5348,5407,5478"}}]}]}