package com.example.myapp.service

import android.graphics.Bitmap
import com.google.android.gms.tasks.Tasks
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

@Singleton
class TextRecognitionService @Inject constructor() {
    
    private val recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    
    /**
     * 从图片中识别血糖数值
     * @param bitmap 血糖仪照片
     * @return 识别到的血糖值，如果未识别到返回null
     */
    suspend fun recognizeBloodSugarValue(bitmap: Bitmap): Float? {
        return withContext(Dispatchers.IO) {
            try {
                val image = InputImage.fromBitmap(bitmap, 0)
                val result = suspendCoroutine { continuation ->
                    recognizer.process(image)
                        .addOnSuccessListener { result ->
                            continuation.resume(result)
                        }
                        .addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }

                // 从识别的文本中提取血糖数值
                extractBloodSugarValue(result.text)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }
    
    /**
     * 从文本中提取血糖数值
     * 支持多种格式：6.5、6,5、6.5mmol/L等
     */
    private fun extractBloodSugarValue(text: String): Float? {
        // 清理文本，移除空格和换行
        val cleanText = text.replace("\\s+".toRegex(), " ").trim()
        
        // 多种正则表达式模式来匹配血糖值
        val patterns = listOf(
            // 匹配 6.5 mmol/L 格式
            "([0-9]+[.,][0-9]+)\\s*mmol/L".toRegex(RegexOption.IGNORE_CASE),
            // 匹配 6.5 mg/dL 格式 (需要转换)
            "([0-9]+[.,][0-9]+)\\s*mg/dL".toRegex(RegexOption.IGNORE_CASE),
            // 匹配纯数字格式 (小数点)
            "([0-9]+\\.[0-9]+)".toRegex(),
            // 匹配纯数字格式 (逗号作为小数点)
            "([0-9]+,[0-9]+)".toRegex(),
            // 匹配整数格式
            "([0-9]+)".toRegex()
        )
        
        for (pattern in patterns) {
            val matches = pattern.findAll(cleanText)
            for (match in matches) {
                val valueStr = match.groupValues[1].replace(",", ".")
                val value = valueStr.toFloatOrNull()
                
                if (value != null) {
                    // 如果是mg/dL单位，转换为mmol/L (除以18)
                    val convertedValue = if (pattern.pattern.contains("mg/dL", true)) {
                        value / 18f
                    } else {
                        value
                    }
                    
                    // 验证血糖值是否在合理范围内 (1.0-30.0 mmol/L)
                    if (convertedValue in 1.0f..30.0f) {
                        return convertedValue
                    }
                }
            }
        }
        
        return null
    }
    
    /**
     * 获取识别到的所有文本（用于调试）
     */
    suspend fun recognizeAllText(bitmap: Bitmap): String? {
        return withContext(Dispatchers.IO) {
            try {
                val image = InputImage.fromBitmap(bitmap, 0)
                val result = suspendCoroutine { continuation ->
                    recognizer.process(image)
                        .addOnSuccessListener { result ->
                            continuation.resume(result)
                        }
                        .addOnFailureListener { exception ->
                            continuation.resumeWithException(exception)
                        }
                }
                result.text
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }
}
