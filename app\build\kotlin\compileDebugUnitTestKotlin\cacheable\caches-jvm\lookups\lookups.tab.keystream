  
BloodSugar com.example.myapp  BloodSugarUnitTest com.example.myapp  Date com.example.myapp  MeasurementType com.example.myapp  Test com.example.myapp  assertEquals com.example.myapp  assertFalse com.example.myapp  
assertTrue com.example.myapp  rangeTo com.example.myapp  
BloodSugar $com.example.myapp.BloodSugarUnitTest  Date $com.example.myapp.BloodSugarUnitTest  MeasurementType $com.example.myapp.BloodSugarUnitTest  assertEquals $com.example.myapp.BloodSugarUnitTest  assertFalse $com.example.myapp.BloodSugarUnitTest  
assertTrue $com.example.myapp.BloodSugarUnitTest  rangeTo $com.example.myapp.BloodSugarUnitTest  
BloodSugar com.example.myapp.data.entity  MeasurementType com.example.myapp.data.entity  id (com.example.myapp.data.entity.BloodSugar  measurementType (com.example.myapp.data.entity.BloodSugar  note (com.example.myapp.data.entity.BloodSugar  value (com.example.myapp.data.entity.BloodSugar  AFTER_BREAKFAST -com.example.myapp.data.entity.MeasurementType  AFTER_DINNER -com.example.myapp.data.entity.MeasurementType  AFTER_LUNCH -com.example.myapp.data.entity.MeasurementType  BEFORE_BREAKFAST -com.example.myapp.data.entity.MeasurementType  
BEFORE_DINNER -com.example.myapp.data.entity.MeasurementType  BEFORE_LUNCH -com.example.myapp.data.entity.MeasurementType  BEFORE_SLEEP -com.example.myapp.data.entity.MeasurementType  FASTING -com.example.myapp.data.entity.MeasurementType  RANDOM -com.example.myapp.data.entity.MeasurementType  displayName -com.example.myapp.data.entity.MeasurementType  Date 	java.util  	compareTo kotlin.Float  rangeTo kotlin.Float  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  rangeTo 
kotlin.ranges  contains &kotlin.ranges.ClosedFloatingPointRange  
BloodSugar 	org.junit  Date 	org.junit  MeasurementType 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertFalse 	org.junit  
assertTrue 	org.junit  rangeTo 	org.junit  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertTrue org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         