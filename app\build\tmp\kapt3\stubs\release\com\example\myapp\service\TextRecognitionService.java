package com.example.myapp.service;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\u0017\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002\u00a2\u0006\u0002\u0010\tJ\u0018\u0010\n\u001a\u0004\u0018\u00010\b2\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/myapp/service/TextRecognitionService;", "", "()V", "recognizer", "Lcom/google/mlkit/vision/text/TextRecognizer;", "extractBloodSugarValue", "", "text", "", "(Ljava/lang/String;)Ljava/lang/Float;", "recognizeAllText", "bitmap", "Landroid/graphics/Bitmap;", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recognizeBloodSugarValue", "app_release"})
public final class TextRecognitionService {
    @org.jetbrains.annotations.NotNull()
    private final com.google.mlkit.vision.text.TextRecognizer recognizer = null;
    
    @javax.inject.Inject()
    public TextRecognitionService() {
        super();
    }
    
    /**
     * 从图片中识别血糖数值
     * @param bitmap 血糖仪照片
     * @return 识别到的血糖值，如果未识别到返回null
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object recognizeBloodSugarValue(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Float> $completion) {
        return null;
    }
    
    /**
     * 从文本中提取血糖数值
     * 支持多种格式：6.5、6,5、6.5mmol/L等
     */
    private final java.lang.Float extractBloodSugarValue(java.lang.String text) {
        return null;
    }
    
    /**
     * 获取识别到的所有文本（用于调试）
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object recognizeAllText(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
}