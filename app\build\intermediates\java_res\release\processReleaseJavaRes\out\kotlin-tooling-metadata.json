{"schemaVersion": "1.1.0", "buildSystem": "<PERSON><PERSON><PERSON>", "buildSystemVersion": "8.10.2", "buildPlugin": "org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper", "buildPluginVersion": "2.0.0", "projectSettings": {"isHmppEnabled": true, "isCompatibilityMetadataVariantEnabled": false, "isKPMEnabled": false}, "projectTargets": [{"target": "org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget", "platformType": "androidJvm", "extras": {"android": {"sourceCompatibility": "11", "targetCompatibility": "11"}}}]}