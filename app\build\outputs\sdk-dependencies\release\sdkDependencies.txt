# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.0"
  }
  digests {
    sha256: "$\t8\304\252\270\347>\210\207\003\343\347\323\370s\203\377\345\275Smm^<\020\rL\3207\237\317"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.7.0"
  }
  digests {
    sha256: "\343k\216K\203\223\244\255\307N=J\262*\325\243c\226\360\316\242\344\vW4\352\341I7\337\322$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\317{rd|y\225\a\025\210\376\207\004P\377\234\217\022\177%=-HQ\341a\270\000\366z\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "v@;\261Y\237n*\334\233\2007.\373&t\270\313\355\342\260\361\346\350\\J\224\341\f{\224\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\274\224r\375\224K\322\030\027\201\247^\356\373\202\242\326\021K\241Z\223\230\307\207@X\336\231\257\210\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\345\342\237X\020\240m\247\231TR\371\326\r\177\a\317\215\n\350\215\252\217\243y\331V\214\003F\304d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\031s\271\241#\212CnURh\352\273\325v\357\213r\203\241j\r\250x\r_\254|e\267[\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.0"
  }
  digests {
    sha256: ";F+\307`\353\241\200\225eC#,\244g\307\266 \n\t\034[\372\024o\220l\372\274\205c\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.6.6"
  }
  digests {
    sha256: ">Z\201w\332w\377\201\373\370w\313\305S\352B9\226\345\025\365\026\303\327\233\263\254Kx\302\v\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\371\204\031\277\v\331\316\362k\335\027\332\324\016\203\b\351<\352\257B\3671\210\374\215\'7Dh|\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\024\261RX\031At\243<\004\234\252P\326\367\344\352\207\354\2529\241\256T\021\250\033\331W?m3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\005\321o_uQ\021\260\263U\274\006\243\352\245\374%\355\035\322s\360%\352\322\036w\"\322\037m\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\302:_\274\030\026\234\311{\t\300\023r\000\256\034E\317\206(m\000-)\326\327mdu\240j\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\352\2320\2109\370~\030E\257Fb\357<\316/\246\370\313+\271\f\nA]fJl\244\263\271Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.6.6"
  }
  digests {
    sha256: "~?\025\267\nz\301c\225%\231P\352-\344#\030\026\320\376\317\343\245P\204i,\354\252\214,$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\255<\"&\357\263\216}\021b\332)\321J\006\265\240c\rN\313\306\336u\271\243\331\027B\212\253\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\373\207F\\V\315W\300\002f\005\367\267\346\356\006!\314t\024\320\026x\355\344\376V\262J.\360\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\341K\033\316\270\351z\234+\326\376s=\r\242^Q\274\365\210\331a\231\334\361\320m6\350Q\220;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.04.01"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.2.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.2.1"
  }
  digests {
    sha256: "\323D\340\3270`\315\344\273\300\355d\024i\206\312\037\256\016\016\311S\255\226\230q>\335+\321\026\000"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\bO\237O\003\242\332Y;\315v\372!\310r\005C\255\377\027\322}2\242\235\257\343\322\303\251\267\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\235:\344\336\034\336\312\343\034N[\f\271\255\360\273\001\255U%\252\207\004t\034\036\275T\301\'\223\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-android"
    version: "2.48"
  }
  digests {
    sha256: "$v\217Z\'\306\r\310\261\211G(\206\177\304\307\r\372X\243\204\331\377`\311!,\344\212!7\352"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger"
    version: "2.48"
  }
  digests {
    sha256: "\037\242&\322\264\240,\310\tP\372MI\244\2425\314\216\316\324\231\265\201\3745\212UDj\203\365y"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger-lint-aar"
    version: "2.48"
  }
  digests {
    sha256: "\346!\301\003\277a\264Vo?}\274\200U\253\314\356\361X\201\001\235\024\233\206z\331\355\364\a\250\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-core"
    version: "2.48"
  }
  digests {
    sha256: "\312\202\2453\v7%/=\336\321\345\316\005\367D\263T\253\021\266\307\344\233\332\301\b\226\305\0313a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.1"
  }
  digests {
    sha256: "\202\260G\200\355\242\033\n^\352\002GT\025\210z\034\255\3736\2137\022!\256\264\a\003\300g\3638"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation-compose"
    version: "1.1.0"
  }
  digests {
    sha256: "\"1\324\\\331|\r\354\344L\270Z\270.r\336\275\277\366\277\244)I>\214td\235\022\031/E"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation"
    version: "1.1.0"
  }
  digests {
    sha256: "F\241\321$\030\367zuO\311\034\\(\254\277\004\034x\366N\240\203\361\372\2234\235\216C\366\352\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.6"
  }
  digests {
    sha256: "\333\255-K\312\257\260\r\r\346\274o\315\n\030\207\025j{\257\343<\242\313\345\203A\355\300\324:\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.6"
  }
  digests {
    sha256: "3\315U\3019Qr$\2376\255\231\037s\337x\213B\016\374\302\250/\022\310\244\027\365\317\364\b,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\311\325\267\331+ \030\252\351\205\027\266\003\244E\034\347\225\221B\\\020\227\376\024+\301,\352\vS\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.7.6"
  }
  digests {
    sha256: "\344\331\325\253S\207\022l\227\004\326\326\352\347\257\342\037^\307;>\376\235\240\326\205_4-\a\300\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\231o\242es\367\205\025\250\322\365.\272\037\262G\031\a\bd\2471[\347\022b\032WDU\347V"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-core"
    version: "1.3.1"
  }
  digests {
    sha256: "k~\242\332|\305\004\326bL<\022\240\302\324\210\335f5V4!\332\313\240y\003\231PtC\350"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.6.3"
  }
  digests {
    sha256: "\016\225\037\356\2141\366\002p\274FU:\205\206\000\033{\223\333\261*\354\0067:\251\232\025\003\222\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-camera2"
    version: "1.3.1"
  }
  digests {
    sha256: "\340\344\n\346o\256\251\250\022\310.\270e\311W\024\362\344\367\310J\327\367\327\251/\000B,\276_}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-lifecycle"
    version: "1.3.1"
  }
  digests {
    sha256: "\357\\\367\\\354j\\r\366#\030`\256\233\25059>\210\032L7\223\207w\331F\235\356\"\244\266"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-view"
    version: "1.3.1"
  }
  digests {
    sha256: "Bz\n\260e3\251\257\371\310\'\332\323\235f\005<\347\347\003_\344Q\276}\327\270\243\253\236\236X"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-video"
    version: "1.3.1"
  }
  digests {
    sha256: "\213*\205\236\301U\316G<\247\001\247/\341\266\311>\207\\y\b\233\272\rW4\302\027\v\356\352}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "text-recognition"
    version: "16.0.0"
  }
  digests {
    sha256: "\3207\365\205_\250Sd\036\235\264\323R5t\262\226t\221d\260%\271\344\265u@\241o$\223v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.1.0"
  }
  digests {
    sha256: "N\312V\316\354\3242Z7l\330C\257V7~#v\316(M\fo\005\245\320\250/L\033\370\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.1.0"
  }
  digests {
    sha256: "\003\272\025\363\206\317&\v\336r\374@\020\373\211\250 \304}Q\316c\274\t)~\253\346\314\340\233\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.2"
  }
  digests {
    sha256: "\003F\\\3656\200\332#\323\324\243\231\\\003)\267\367;!\0358y\036\3136\345\221\376\273\211fd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-text-recognition"
    version: "19.0.0"
  }
  digests {
    sha256: "\r$\362\227+\006\266v\033\264x/\202*w\357\0247\2102\215n\225\252\330-H\255\276\206\3662"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-text-recognition-common"
    version: "19.0.0"
  }
  digests {
    sha256: "\206ocr\233CmM \311\346\236\210\313\213\231\345\370\310\t\276\021h\233\rND\311B$\213\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "2.2.1"
  }
  digests {
    sha256: "\346/s\272\034\177]\025\365\330m\216\232)\214B\314\320d\216|\346\v\2361/.\315\022;^\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "2.3.3"
  }
  digests {
    sha256: "\262*\024\325`\245\220\334\207^\220$$\262\232\023\240-\037\257){|9\251\000yg\377\255\314\275"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "2.2.6"
  }
  digests {
    sha256: "\330:\322<\023D)i\021a\311$v\243\310=\3626\315\037\213\255\277\324\360\a#\264\354\315t\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "16.1.0"
  }
  digests {
    sha256: "\217\211\247B\230\273\314\302\305C8R\b\242\360\306\177`<\346~n\302\034\307r\351\216h\217|\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "17.1.0"
  }
  digests {
    sha256: "\202\225\307U\274\255\310z*\256\345\271\345h\362\177\3218\375E\377\021Y\250\356t|\364\303\243\215\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.odml"
    artifactId: "image"
    version: "1.0.0-beta1"
  }
  digests {
    sha256: ".q\2521\370:\224\025\'\177\021\235\346q\225ro\a\321v\016\225B\301\021w\2142\016:\241\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "16.1.0"
  }
  digests {
    sha256: "\202\351\032\245\355\030n\355\257\037\"\020\365\322A\272nDV\317\f2\224\235\\\333\341R\260\342\350\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.0.0"
  }
  digests {
    sha256: "\2100\362\350\245\3444\313\305\344\275\030\0230s\266\350Q\212\214\350\354,\346\363\262s\311\035\226d\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "common"
    version: "18.8.0"
  }
  digests {
    sha256: "\235\3371\237s+!\371i\370\022\245\216\035\245$\363\033& \255\272~\256\177o\326\352#\356\221\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-common"
    version: "17.3.0"
  }
  digests {
    sha256: "\300\b\\\245\373\240\017\021R\234\n\203\261(Q\352lY\233\274\310\257\375\222~\221K)\247CE\237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-interfaces"
    version: "16.2.0"
  }
  digests {
    sha256: "*\320\364xM\255$J\250\205\332\341}P\213\330T\301\331v\276 Z\222\332\030R\350\237\256\036\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "text-recognition-bundled-common"
    version: "16.0.0"
  }
  digests {
    sha256: "\325^u\202\270\373)\021U`\002\335j\335\275\240|\347\366TF\343\222\215\236;h\375\226\320@K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose"
    version: "2.5.0"
  }
  digests {
    sha256: "\221\177@G\\\033 la\244\2115X3\341\021\346\375\347)\354\v\216<\\\312\271#\262\203d/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\020h\no\000\253c\006Sz&\207\"*\260\377b\260\255\203\355}\345\360)G\322\331\325\302\004\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-drawablepainter"
    version: "0.32.0"
  }
  digests {
    sha256: "h\r\'\225\017\221\272\030j\241E\"\255\001\235\255\222\307\002$\215\021\016\275\326\304\227sJ\275}\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\2739\237\2009\350\317\177\025\221\002\310[D(\033\342\377~\274X!@V\370`\034\244?\276\253\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil"
    version: "2.5.0"
  }
  digests {
    sha256: "\304\243\306^\301\275T0~V\356-D\177[kH\214V\361\373\373Z\020\316\301\320\3443\f\3413"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-permissions"
    version: "0.32.0"
  }
  digests {
    sha256: "\264\324\r\243\371\331\267\221h\271\317\r\252\a2\000\3509\356|\215x\213\351\233<\370u\356\255\302D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier"
    version: "1.4.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier-android"
    version: "1.4.1"
  }
  digests {
    sha256: "&\234\307\264:.K]7\337)8\322u-)F\276\251\271\217\373\200\3540\257\025\003\200E\037L"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 72
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 2
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 24
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 19
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 25
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 43
}
library_dependencies {
  library_index: 26
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 43
}
library_dependencies {
  library_index: 27
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 43
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 30
  library_dep_index: 9
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 24
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 25
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 31
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
  library_dep_index: 6
  library_dep_index: 48
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 55
  library_dep_index: 51
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 59
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 51
  library_dep_index: 64
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 5
  library_dep_index: 28
  library_dep_index: 42
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 38
  library_dep_index: 47
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 46
  library_dep_index: 40
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 47
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 27
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 42
  library_dep_index: 39
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 44
  library_dep_index: 14
}
library_dependencies {
  library_index: 47
  library_dep_index: 37
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 55
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 55
  library_dep_index: 51
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 51
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 61
  library_dep_index: 51
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 51
  library_dep_index: 8
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 51
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 59
  library_dep_index: 59
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 55
  library_dep_index: 51
}
library_dependencies {
  library_index: 63
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 66
  library_dep_index: 70
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 57
  library_dep_index: 51
  library_dep_index: 8
  library_dep_index: 59
  library_dep_index: 4
  library_dep_index: 70
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 49
  library_dep_index: 51
  library_dep_index: 4
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 55
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 66
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 68
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 51
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 64
}
library_dependencies {
  library_index: 72
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 53
  library_dep_index: 61
  library_dep_index: 66
  library_dep_index: 70
  library_dep_index: 64
  library_dep_index: 75
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 54
  library_dep_index: 62
  library_dep_index: 67
  library_dep_index: 71
  library_dep_index: 65
  library_dep_index: 68
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 57
  library_dep_index: 51
  library_dep_index: 49
  library_dep_index: 55
  library_dep_index: 69
  library_dep_index: 77
  library_dep_index: 79
  library_dep_index: 58
  library_dep_index: 52
  library_dep_index: 50
  library_dep_index: 56
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 47
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 68
  library_dep_index: 64
  library_dep_index: 70
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 31
  library_dep_index: 53
  library_dep_index: 57
  library_dep_index: 51
  library_dep_index: 24
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 41
  library_dep_index: 4
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 66
  library_dep_index: 64
  library_dep_index: 31
  library_dep_index: 51
  library_dep_index: 4
  library_dep_index: 76
}
library_dependencies {
  library_index: 80
  library_dep_index: 9
  library_dep_index: 18
  library_dep_index: 81
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 81
  library_dep_index: 6
  library_dep_index: 2
  library_dep_index: 82
  library_dep_index: 80
}
library_dependencies {
  library_index: 82
  library_dep_index: 81
  library_dep_index: 80
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 81
  library_dep_index: 80
}
library_dependencies {
  library_index: 83
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 6
  library_dep_index: 83
  library_dep_index: 0
  library_dep_index: 83
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 91
  library_dep_index: 19
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 87
  library_dep_index: 0
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 89
  library_dep_index: 86
  library_dep_index: 90
  library_dep_index: 87
}
library_dependencies {
  library_index: 91
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 92
  library_dep_index: 40
  library_dep_index: 93
  library_dep_index: 0
}
library_dependencies {
  library_index: 92
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 29
}
library_dependencies {
  library_index: 93
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 95
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 96
  library_dep_index: 30
  library_dep_index: 100
  library_dep_index: 0
}
library_dependencies {
  library_index: 96
  library_dep_index: 6
  library_dep_index: 97
  library_dep_index: 85
  library_dep_index: 0
}
library_dependencies {
  library_index: 97
  library_dep_index: 37
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 28
  library_dep_index: 42
  library_dep_index: 98
  library_dep_index: 0
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 101
}
library_dependencies {
  library_index: 98
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 28
  library_dep_index: 42
  library_dep_index: 39
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 97
  library_dep_index: 101
}
library_dependencies {
  library_index: 99
  library_dep_index: 98
  library_dep_index: 98
  library_dep_index: 100
  library_dep_index: 97
  library_dep_index: 101
}
library_dependencies {
  library_index: 100
  library_dep_index: 47
  library_dep_index: 66
  library_dep_index: 70
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 30
  library_dep_index: 101
  library_dep_index: 0
  library_dep_index: 101
  library_dep_index: 97
  library_dep_index: 99
  library_dep_index: 98
}
library_dependencies {
  library_index: 101
  library_dep_index: 99
  library_dep_index: 97
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 102
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 103
  library_dep_index: 19
  library_dep_index: 25
  library_dep_index: 104
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 105
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 115
}
library_dependencies {
  library_index: 103
  library_dep_index: 6
}
library_dependencies {
  library_index: 105
  library_dep_index: 6
  library_dep_index: 102
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 104
  library_dep_index: 14
  library_dep_index: 102
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 115
}
library_dependencies {
  library_index: 106
  library_dep_index: 102
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 19
  library_dep_index: 104
  library_dep_index: 14
  library_dep_index: 105
  library_dep_index: 102
  library_dep_index: 107
  library_dep_index: 115
}
library_dependencies {
  library_index: 107
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 108
  library_dep_index: 102
  library_dep_index: 106
  library_dep_index: 115
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 19
  library_dep_index: 104
  library_dep_index: 14
  library_dep_index: 105
  library_dep_index: 102
  library_dep_index: 106
  library_dep_index: 115
}
library_dependencies {
  library_index: 108
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 109
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 91
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 114
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 109
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 108
}
library_dependencies {
  library_index: 110
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 111
  library_dep_index: 110
  library_dep_index: 15
  library_dep_index: 10
}
library_dependencies {
  library_index: 112
  library_dep_index: 6
}
library_dependencies {
  library_index: 113
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 94
}
library_dependencies {
  library_index: 114
  library_dep_index: 6
}
library_dependencies {
  library_index: 115
  library_dep_index: 6
  library_dep_index: 102
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 102
  library_dep_index: 106
  library_dep_index: 107
}
library_dependencies {
  library_index: 116
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 120
  library_dep_index: 130
  library_dep_index: 133
}
library_dependencies {
  library_index: 117
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 91
  library_dep_index: 118
  library_dep_index: 119
}
library_dependencies {
  library_index: 118
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 91
}
library_dependencies {
  library_index: 119
  library_dep_index: 118
}
library_dependencies {
  library_index: 120
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 121
  library_dep_index: 130
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
  library_dep_index: 123
  library_dep_index: 124
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 127
  library_dep_index: 128
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 130
  library_dep_index: 131
  library_dep_index: 132
}
library_dependencies {
  library_index: 122
  library_dep_index: 6
}
library_dependencies {
  library_index: 123
  library_dep_index: 6
  library_dep_index: 122
  library_dep_index: 124
  library_dep_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 124
  library_dep_index: 6
  library_dep_index: 122
  library_dep_index: 87
}
library_dependencies {
  library_index: 125
  library_dep_index: 6
}
library_dependencies {
  library_index: 126
  library_dep_index: 6
  library_dep_index: 125
}
library_dependencies {
  library_index: 128
  library_dep_index: 6
  library_dep_index: 129
}
library_dependencies {
  library_index: 130
  library_dep_index: 8
  library_dep_index: 122
  library_dep_index: 123
  library_dep_index: 124
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 128
  library_dep_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 131
  library_dep_index: 103
  library_dep_index: 122
  library_dep_index: 123
  library_dep_index: 124
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 127
  library_dep_index: 128
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 130
}
library_dependencies {
  library_index: 132
  library_dep_index: 118
  library_dep_index: 119
}
library_dependencies {
  library_index: 133
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 134
  library_dep_index: 135
  library_dep_index: 141
  library_dep_index: 2
}
library_dependencies {
  library_index: 135
  library_dep_index: 5
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 64
  library_dep_index: 2
}
library_dependencies {
  library_index: 136
  library_dep_index: 35
  library_dep_index: 20
  library_dep_index: 2
}
library_dependencies {
  library_index: 137
  library_dep_index: 6
  library_dep_index: 109
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 103
  library_dep_index: 46
  library_dep_index: 16
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 138
  library_dep_index: 139
}
library_dependencies {
  library_index: 138
  library_dep_index: 139
  library_dep_index: 2
}
library_dependencies {
  library_index: 139
  library_dep_index: 140
}
library_dependencies {
  library_index: 140
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 141
  library_dep_index: 137
  library_dep_index: 2
}
library_dependencies {
  library_index: 142
  library_dep_index: 47
  library_dep_index: 64
  library_dep_index: 20
  library_dep_index: 143
  library_dep_index: 2
}
library_dependencies {
  library_index: 143
  library_dep_index: 144
}
library_dependencies {
  library_index: 144
  library_dep_index: 0
  library_dep_index: 4
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 28
  dependency_index: 47
  dependency_index: 73
  dependency_index: 35
  dependency_index: 53
  dependency_index: 61
  dependency_index: 74
  dependency_index: 80
  dependency_index: 82
  dependency_index: 85
  dependency_index: 95
  dependency_index: 100
  dependency_index: 30
  dependency_index: 102
  dependency_index: 105
  dependency_index: 106
  dependency_index: 107
  dependency_index: 116
  dependency_index: 134
  dependency_index: 142
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
