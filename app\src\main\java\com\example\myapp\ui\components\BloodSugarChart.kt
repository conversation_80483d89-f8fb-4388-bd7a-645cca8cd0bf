package com.example.myapp.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.myapp.data.entity.BloodSugar
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max
import kotlin.math.min

@Composable
fun BloodSugarChart(
    bloodSugars: List<BloodSugar>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "血糖趋势图",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (bloodSugars.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Text(
                        text = "暂无数据",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                SimpleLineChart(
                    bloodSugars = bloodSugars,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                )
            }
        }
    }
}

@Composable
private fun SimpleLineChart(
    bloodSugars: List<BloodSugar>,
    modifier: Modifier = Modifier
) {
    val sortedData = bloodSugars.sortedBy { it.dateTime }
    
    if (sortedData.isEmpty()) return
    
    val maxValue = sortedData.maxOf { it.value }
    val minValue = sortedData.minOf { it.value }
    val valueRange = maxValue - minValue
    
    Canvas(modifier = modifier) {
        val width = size.width
        val height = size.height
        val padding = 40f
        
        val chartWidth = width - 2 * padding
        val chartHeight = height - 2 * padding
        
        // 绘制坐标轴
        drawLine(
            color = Color.Gray,
            start = Offset(padding, height - padding),
            end = Offset(width - padding, height - padding),
            strokeWidth = 2f
        )
        drawLine(
            color = Color.Gray,
            start = Offset(padding, padding),
            end = Offset(padding, height - padding),
            strokeWidth = 2f
        )
        
        // 绘制参考线（正常血糖范围）
        val normalMin = 3.9f
        val normalMax = 7.8f
        
        if (normalMin >= minValue && normalMin <= maxValue) {
            val y = height - padding - ((normalMin - minValue) / valueRange) * chartHeight
            drawLine(
                color = Color.Green.copy(alpha = 0.5f),
                start = Offset(padding, y),
                end = Offset(width - padding, y),
                strokeWidth = 1f
            )
        }
        
        if (normalMax >= minValue && normalMax <= maxValue) {
            val y = height - padding - ((normalMax - minValue) / valueRange) * chartHeight
            drawLine(
                color = Color.Green.copy(alpha = 0.5f),
                start = Offset(padding, y),
                end = Offset(width - padding, y),
                strokeWidth = 1f
            )
        }
        
        // 绘制数据点和连线
        if (sortedData.size > 1) {
            val path = Path()
            var isFirst = true
            
            sortedData.forEachIndexed { index, bloodSugar ->
                val x = padding + (index.toFloat() / (sortedData.size - 1)) * chartWidth
                val y = height - padding - ((bloodSugar.value - minValue) / valueRange) * chartHeight
                
                if (isFirst) {
                    path.moveTo(x, y)
                    isFirst = false
                } else {
                    path.lineTo(x, y)
                }
                
                // 绘制数据点
                val pointColor = when {
                    bloodSugar.value < 3.9f -> Color.Red
                    bloodSugar.value > 7.8f -> Color.Red
                    else -> Color.Blue
                }
                
                drawCircle(
                    color = pointColor,
                    radius = 4f,
                    center = Offset(x, y)
                )
            }
            
            // 绘制连线
            drawPath(
                path = path,
                color = Color.Blue,
                style = Stroke(width = 2f)
            )
        }
    }
}
