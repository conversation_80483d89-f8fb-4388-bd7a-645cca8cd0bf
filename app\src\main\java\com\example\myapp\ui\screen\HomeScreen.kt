package com.example.myapp.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.myapp.ui.components.AddBloodSugarDialog
import com.example.myapp.ui.components.BloodSugarChart
import com.example.myapp.ui.components.BloodSugarItem
import com.example.myapp.ui.components.EditBloodSugarDialog
import com.example.myapp.ui.components.StatisticsCard
import com.example.myapp.data.entity.BloodSugar
import com.example.myapp.ui.viewmodel.BloodSugarViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: BloodSugarViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showAddDialog by remember { mutableStateOf(false) }
    var editingBloodSugar by remember { mutableStateOf<BloodSugar?>(null) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("血糖记录") },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { showAddDialog = true },
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加血糖记录"
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 统计卡片
            StatisticsCard(
                statistics = uiState.statistics,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 图表
            BloodSugarChart(
                bloodSugars = uiState.bloodSugars.take(10), // 显示最近10条记录
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 记录列表
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (uiState.bloodSugars.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "还没有血糖记录",
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "点击右下角的 + 按钮添加第一条记录",
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(uiState.bloodSugars) { bloodSugar ->
                        BloodSugarItem(
                            bloodSugar = bloodSugar,
                            onEdit = { editingBloodSugar = it },
                            onDelete = { viewModel.deleteBloodSugar(it) }
                        )
                    }
                }
            }
        }
    }
    
    // 添加记录对话框
    if (showAddDialog) {
        AddBloodSugarDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { value, type, dateTime, note ->
                viewModel.addBloodSugar(value, type, dateTime, note)
                showAddDialog = false
            }
        )
    }

    // 编辑记录对话框
    editingBloodSugar?.let { bloodSugar ->
        EditBloodSugarDialog(
            bloodSugar = bloodSugar,
            onDismiss = { editingBloodSugar = null },
            onConfirm = { updatedBloodSugar ->
                viewModel.updateBloodSugar(updatedBloodSugar)
                editingBloodSugar = null
            }
        )
    }
    
    // 错误提示
    uiState.errorMessage?.let { message ->
        LaunchedEffect(message) {
            // 这里可以显示 SnackBar 或其他错误提示
            viewModel.clearError()
        }
    }
}
