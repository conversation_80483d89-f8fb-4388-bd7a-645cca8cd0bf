package com.example.myapp

import com.example.myapp.data.entity.BloodSugar
import com.example.myapp.data.entity.MeasurementType
import org.junit.Test
import org.junit.Assert.*
import java.util.Date

/**
 * 血糖记录APP单元测试
 */
class BloodSugarUnitTest {

    @Test
    fun bloodSugar_creation_isCorrect() {
        val bloodSugar = BloodSugar(
            id = 1,
            value = 6.5f,
            measurementType = MeasurementType.FASTING,
            dateTime = Date(),
            note = "测试记录"
        )

        assertEquals(1, bloodSugar.id)
        assertEquals(6.5f, bloodSugar.value, 0.01f)
        assertEquals(MeasurementType.FASTING, bloodSugar.measurementType)
        assertEquals("测试记录", bloodSugar.note)
    }

    @Test
    fun measurementType_displayName_isCorrect() {
        assertEquals("空腹", MeasurementType.FASTING.displayName)
        assertEquals("早餐前", MeasurementType.BEFORE_BREAKFAST.displayName)
        assertEquals("早餐后", MeasurementType.AFTER_BREAKFAST.displayName)
        assertEquals("午餐前", MeasurementType.BEFORE_LUNCH.displayName)
        assertEquals("午餐后", MeasurementType.AFTER_LUNCH.displayName)
        assertEquals("晚餐前", MeasurementType.BEFORE_DINNER.displayName)
        assertEquals("晚餐后", MeasurementType.AFTER_DINNER.displayName)
        assertEquals("睡前", MeasurementType.BEFORE_SLEEP.displayName)
        assertEquals("随机", MeasurementType.RANDOM.displayName)
    }

    @Test
    fun bloodSugar_normalRange_validation() {
        val normalValue = 6.0f
        val lowValue = 3.5f
        val highValue = 8.5f

        assertTrue("正常血糖值应在3.9-7.8范围内", normalValue in 3.9f..7.8f)
        assertFalse("低血糖值应小于3.9", lowValue >= 3.9f)
        assertFalse("高血糖值应大于7.8", highValue <= 7.8f)
    }
}