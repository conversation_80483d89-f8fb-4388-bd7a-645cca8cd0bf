package com.example.myapp.data.repository

import com.example.myapp.data.dao.BloodSugarDao
import com.example.myapp.data.entity.BloodSugar
import com.example.myapp.data.entity.MeasurementType
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BloodSugarRepository @Inject constructor(
    private val bloodSugarDao: BloodSugarDao
) {
    
    fun getAllBloodSugars(): Flow<List<BloodSugar>> = bloodSugarDao.getAllBloodSugars()
    
    suspend fun getBloodSugarById(id: Long): BloodSugar? = bloodSugarDao.getBloodSugarById(id)
    
    fun getBloodSugarsByDateRange(startDate: Date, endDate: Date): Flow<List<BloodSugar>> =
        bloodSugarDao.getBloodSugarsByDateRange(startDate, endDate)
    
    fun getBloodSugarsByType(type: MeasurementType): Flow<List<BloodSugar>> =
        bloodSugarDao.getBloodSugarsByType(type)
    
    suspend fun getAverageBloodSugar(startDate: Date, endDate: Date): Float? =
        bloodSugarDao.getAverageBloodSugar(startDate, endDate)
    
    suspend fun getMaxBloodSugar(startDate: Date, endDate: Date): Float? =
        bloodSugarDao.getMaxBloodSugar(startDate, endDate)
    
    suspend fun getMinBloodSugar(startDate: Date, endDate: Date): Float? =
        bloodSugarDao.getMinBloodSugar(startDate, endDate)
    
    suspend fun getRecordCount(startDate: Date, endDate: Date): Int =
        bloodSugarDao.getRecordCount(startDate, endDate)
    
    suspend fun insertBloodSugar(bloodSugar: BloodSugar): Long =
        bloodSugarDao.insertBloodSugar(bloodSugar)
    
    suspend fun updateBloodSugar(bloodSugar: BloodSugar) =
        bloodSugarDao.updateBloodSugar(bloodSugar)
    
    suspend fun deleteBloodSugar(bloodSugar: BloodSugar) =
        bloodSugarDao.deleteBloodSugar(bloodSugar)
    
    suspend fun deleteBloodSugarById(id: Long) =
        bloodSugarDao.deleteBloodSugarById(id)
}
