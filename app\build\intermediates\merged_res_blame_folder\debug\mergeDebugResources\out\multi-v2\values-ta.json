{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-68:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0233180bfb2a73e0304a16e4c911a11f\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "36,37,56,57,58,59,60,117,118,119,120,121,122,124,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3617,3714,6034,6128,6229,6320,6403,12974,13065,13160,13240,13319,13401,13569,13760,13840,13909", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "3709,3793,6123,6224,6315,6398,6507,13060,13155,13235,13314,13396,13482,13654,13835,13904,14024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22f038d89e49e38792143e9fe11d5050\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,13487", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,13564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\86b2aff77130131bb6ebfada36b55526\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "129,130", "startColumns": "4,4", "startOffsets": "14029,14122", "endColumns": "92,97", "endOffsets": "14117,14215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d0effdc45e183036880fc61940e99b3\\transformed\\core-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "29,30,31,32,33,34,35,125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2871,2967,3070,3169,3267,3374,3489,13659", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "2962,3065,3164,3262,3369,3484,3612,13755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9a76451986ca9c11d04c7076059da51\\transformed\\play-services-basement-18.1.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4782", "endColumns": "160", "endOffsets": "4938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abc133dde975a2a52fdd6f2f6c3ad94d\\transformed\\play-services-base-18.1.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3798,3903,4055,4182,4288,4440,4568,4681,4943,5123,5230,5383,5518,5672,5828,5890,5953", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "3898,4050,4177,4283,4435,4563,4676,4777,5118,5225,5378,5513,5667,5823,5885,5948,6029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6e00bb0cb13b50e7a03e9d56b3ab5729\\transformed\\material3-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4876,4969,5082,5162,5250,5349,5469,5564,5669,5758,5880,5984,6091,6224,6304,6415", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4871,4964,5077,5157,5245,5344,5464,5559,5664,5753,5875,5979,6086,6219,6299,6410,6512"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6512,6638,6764,6885,7009,7110,7206,7319,7470,7601,7742,7826,7930,8030,8138,8255,8378,8487,8633,8777,8911,9117,9246,9367,9492,9638,9739,9837,9983,10119,10225,10338,10445,10591,10743,10852,10964,11042,11144,11247,11333,11426,11539,11619,11707,11806,11926,12021,12126,12215,12337,12441,12548,12681,12761,12872", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "6633,6759,6880,7004,7105,7201,7314,7465,7596,7737,7821,7925,8025,8133,8250,8373,8482,8628,8772,8906,9112,9241,9362,9487,9633,9734,9832,9978,10114,10220,10333,10440,10586,10738,10847,10959,11037,11139,11242,11328,11421,11534,11614,11702,11801,11921,12016,12121,12210,12332,12436,12543,12676,12756,12867,12969"}}]}]}