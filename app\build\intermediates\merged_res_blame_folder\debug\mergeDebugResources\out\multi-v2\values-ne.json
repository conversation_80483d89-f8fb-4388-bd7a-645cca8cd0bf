{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-57:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\195d9e3dd5a9d6ba896447ff14efbb05\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4828,4923,5030,5117,5202,5303,5412,5507,5610,5697,5808,5907,6012,6147,6232,6336", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,90,94,106,86,84,100,108,94,102,86,110,98,104,134,84,103,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4823,4918,5025,5112,5197,5298,5407,5502,5605,5692,5803,5902,6007,6142,6227,6331,6425"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1598,1717,1833,1961,2060,2155,2267,2419,2540,2693,2777,2885,2983,3082,3194,3318,3431,3577,3720,3854,4019,4149,4301,4458,4587,4686,4781,4897,5021,5125,5244,5354,5500,5648,5758,5866,5941,6046,6151,6242,6337,6444,6531,6616,6717,6826,6921,7024,7111,7222,7321,7426,7561,7646,7750", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,90,94,106,86,84,100,108,94,102,86,110,98,104,134,84,103,93", "endOffsets": "1593,1712,1828,1956,2055,2150,2262,2414,2535,2688,2772,2880,2978,3077,3189,3313,3426,3572,3715,3849,4014,4144,4296,4453,4582,4681,4776,4892,5016,5120,5239,5349,5495,5643,5753,5861,5936,6041,6146,6237,6332,6439,6526,6611,6712,6821,6916,7019,7106,7217,7316,7421,7556,7641,7745,7839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5f0b3c638ab77a0ce9d8c56647651228\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,924,1014,1108,1205,1291,1373,7844,7931,8017,8083,8149,8239,8332,8510,8591,8659", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "919,1009,1103,1200,1286,1368,1464,7926,8012,8078,8144,8234,8327,8404,8586,8654,8774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36cdb26bea2ca7fcb7382d8ae39b5662\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8779,8864", "endColumns": "84,88", "endOffsets": "8859,8948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cdb9e3e29d5dc7261cbb30e01775346f\\transformed\\core-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,413,519,617,717,8409", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "203,306,408,514,612,712,820,8505"}}]}]}