package com.example.myapp.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.myapp.data.converter.Converters;
import com.example.myapp.data.entity.BloodSugar;
import com.example.myapp.data.entity.MeasurementType;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class BloodSugarDao_Impl implements BloodSugarDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<BloodSugar> __insertionAdapterOfBloodSugar;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<BloodSugar> __deletionAdapterOfBloodSugar;

  private final EntityDeletionOrUpdateAdapter<BloodSugar> __updateAdapterOfBloodSugar;

  private final SharedSQLiteStatement __preparedStmtOfDeleteBloodSugarById;

  public BloodSugarDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfBloodSugar = new EntityInsertionAdapter<BloodSugar>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `blood_sugar` (`id`,`value`,`measurementType`,`dateTime`,`note`,`meterPhotoPath`,`foodPhotoPath`) VALUES (nullif(?, 0),?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BloodSugar entity) {
        statement.bindLong(1, entity.getId());
        statement.bindDouble(2, entity.getValue());
        final String _tmp = __converters.fromMeasurementType(entity.getMeasurementType());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getDateTime());
        if (_tmp_1 == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp_1);
        }
        if (entity.getNote() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getNote());
        }
        if (entity.getMeterPhotoPath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getMeterPhotoPath());
        }
        if (entity.getFoodPhotoPath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFoodPhotoPath());
        }
      }
    };
    this.__deletionAdapterOfBloodSugar = new EntityDeletionOrUpdateAdapter<BloodSugar>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `blood_sugar` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BloodSugar entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfBloodSugar = new EntityDeletionOrUpdateAdapter<BloodSugar>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `blood_sugar` SET `id` = ?,`value` = ?,`measurementType` = ?,`dateTime` = ?,`note` = ?,`meterPhotoPath` = ?,`foodPhotoPath` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BloodSugar entity) {
        statement.bindLong(1, entity.getId());
        statement.bindDouble(2, entity.getValue());
        final String _tmp = __converters.fromMeasurementType(entity.getMeasurementType());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        final Long _tmp_1 = __converters.dateToTimestamp(entity.getDateTime());
        if (_tmp_1 == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp_1);
        }
        if (entity.getNote() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getNote());
        }
        if (entity.getMeterPhotoPath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getMeterPhotoPath());
        }
        if (entity.getFoodPhotoPath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFoodPhotoPath());
        }
        statement.bindLong(8, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteBloodSugarById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM blood_sugar WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertBloodSugar(final BloodSugar bloodSugar,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfBloodSugar.insertAndReturnId(bloodSugar);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteBloodSugar(final BloodSugar bloodSugar,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfBloodSugar.handle(bloodSugar);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBloodSugar(final BloodSugar bloodSugar,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfBloodSugar.handle(bloodSugar);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteBloodSugarById(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteBloodSugarById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteBloodSugarById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BloodSugar>> getAllBloodSugars() {
    final String _sql = "SELECT * FROM blood_sugar ORDER BY dateTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"blood_sugar"}, new Callable<List<BloodSugar>>() {
      @Override
      @NonNull
      public List<BloodSugar> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfMeasurementType = CursorUtil.getColumnIndexOrThrow(_cursor, "measurementType");
          final int _cursorIndexOfDateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "dateTime");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfMeterPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "meterPhotoPath");
          final int _cursorIndexOfFoodPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "foodPhotoPath");
          final List<BloodSugar> _result = new ArrayList<BloodSugar>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BloodSugar _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final float _tmpValue;
            _tmpValue = _cursor.getFloat(_cursorIndexOfValue);
            final MeasurementType _tmpMeasurementType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMeasurementType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMeasurementType);
            }
            _tmpMeasurementType = __converters.toMeasurementType(_tmp);
            final Date _tmpDateTime;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDateTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDateTime);
            }
            _tmpDateTime = __converters.fromTimestamp(_tmp_1);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final String _tmpMeterPhotoPath;
            if (_cursor.isNull(_cursorIndexOfMeterPhotoPath)) {
              _tmpMeterPhotoPath = null;
            } else {
              _tmpMeterPhotoPath = _cursor.getString(_cursorIndexOfMeterPhotoPath);
            }
            final String _tmpFoodPhotoPath;
            if (_cursor.isNull(_cursorIndexOfFoodPhotoPath)) {
              _tmpFoodPhotoPath = null;
            } else {
              _tmpFoodPhotoPath = _cursor.getString(_cursorIndexOfFoodPhotoPath);
            }
            _item = new BloodSugar(_tmpId,_tmpValue,_tmpMeasurementType,_tmpDateTime,_tmpNote,_tmpMeterPhotoPath,_tmpFoodPhotoPath);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getBloodSugarById(final long id,
      final Continuation<? super BloodSugar> $completion) {
    final String _sql = "SELECT * FROM blood_sugar WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<BloodSugar>() {
      @Override
      @Nullable
      public BloodSugar call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfMeasurementType = CursorUtil.getColumnIndexOrThrow(_cursor, "measurementType");
          final int _cursorIndexOfDateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "dateTime");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfMeterPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "meterPhotoPath");
          final int _cursorIndexOfFoodPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "foodPhotoPath");
          final BloodSugar _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final float _tmpValue;
            _tmpValue = _cursor.getFloat(_cursorIndexOfValue);
            final MeasurementType _tmpMeasurementType;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfMeasurementType)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfMeasurementType);
            }
            _tmpMeasurementType = __converters.toMeasurementType(_tmp);
            final Date _tmpDateTime;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDateTime)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDateTime);
            }
            _tmpDateTime = __converters.fromTimestamp(_tmp_1);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final String _tmpMeterPhotoPath;
            if (_cursor.isNull(_cursorIndexOfMeterPhotoPath)) {
              _tmpMeterPhotoPath = null;
            } else {
              _tmpMeterPhotoPath = _cursor.getString(_cursorIndexOfMeterPhotoPath);
            }
            final String _tmpFoodPhotoPath;
            if (_cursor.isNull(_cursorIndexOfFoodPhotoPath)) {
              _tmpFoodPhotoPath = null;
            } else {
              _tmpFoodPhotoPath = _cursor.getString(_cursorIndexOfFoodPhotoPath);
            }
            _result = new BloodSugar(_tmpId,_tmpValue,_tmpMeasurementType,_tmpDateTime,_tmpNote,_tmpMeterPhotoPath,_tmpFoodPhotoPath);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BloodSugar>> getBloodSugarsByDateRange(final Date startDate,
      final Date endDate) {
    final String _sql = "SELECT * FROM blood_sugar WHERE dateTime BETWEEN ? AND ? ORDER BY dateTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"blood_sugar"}, new Callable<List<BloodSugar>>() {
      @Override
      @NonNull
      public List<BloodSugar> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfMeasurementType = CursorUtil.getColumnIndexOrThrow(_cursor, "measurementType");
          final int _cursorIndexOfDateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "dateTime");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfMeterPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "meterPhotoPath");
          final int _cursorIndexOfFoodPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "foodPhotoPath");
          final List<BloodSugar> _result = new ArrayList<BloodSugar>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BloodSugar _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final float _tmpValue;
            _tmpValue = _cursor.getFloat(_cursorIndexOfValue);
            final MeasurementType _tmpMeasurementType;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfMeasurementType)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfMeasurementType);
            }
            _tmpMeasurementType = __converters.toMeasurementType(_tmp_2);
            final Date _tmpDateTime;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfDateTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfDateTime);
            }
            _tmpDateTime = __converters.fromTimestamp(_tmp_3);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final String _tmpMeterPhotoPath;
            if (_cursor.isNull(_cursorIndexOfMeterPhotoPath)) {
              _tmpMeterPhotoPath = null;
            } else {
              _tmpMeterPhotoPath = _cursor.getString(_cursorIndexOfMeterPhotoPath);
            }
            final String _tmpFoodPhotoPath;
            if (_cursor.isNull(_cursorIndexOfFoodPhotoPath)) {
              _tmpFoodPhotoPath = null;
            } else {
              _tmpFoodPhotoPath = _cursor.getString(_cursorIndexOfFoodPhotoPath);
            }
            _item = new BloodSugar(_tmpId,_tmpValue,_tmpMeasurementType,_tmpDateTime,_tmpNote,_tmpMeterPhotoPath,_tmpFoodPhotoPath);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BloodSugar>> getBloodSugarsByType(final MeasurementType type) {
    final String _sql = "SELECT * FROM blood_sugar WHERE measurementType = ? ORDER BY dateTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromMeasurementType(type);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"blood_sugar"}, new Callable<List<BloodSugar>>() {
      @Override
      @NonNull
      public List<BloodSugar> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfValue = CursorUtil.getColumnIndexOrThrow(_cursor, "value");
          final int _cursorIndexOfMeasurementType = CursorUtil.getColumnIndexOrThrow(_cursor, "measurementType");
          final int _cursorIndexOfDateTime = CursorUtil.getColumnIndexOrThrow(_cursor, "dateTime");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfMeterPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "meterPhotoPath");
          final int _cursorIndexOfFoodPhotoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "foodPhotoPath");
          final List<BloodSugar> _result = new ArrayList<BloodSugar>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BloodSugar _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final float _tmpValue;
            _tmpValue = _cursor.getFloat(_cursorIndexOfValue);
            final MeasurementType _tmpMeasurementType;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfMeasurementType)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfMeasurementType);
            }
            _tmpMeasurementType = __converters.toMeasurementType(_tmp_1);
            final Date _tmpDateTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDateTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDateTime);
            }
            _tmpDateTime = __converters.fromTimestamp(_tmp_2);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final String _tmpMeterPhotoPath;
            if (_cursor.isNull(_cursorIndexOfMeterPhotoPath)) {
              _tmpMeterPhotoPath = null;
            } else {
              _tmpMeterPhotoPath = _cursor.getString(_cursorIndexOfMeterPhotoPath);
            }
            final String _tmpFoodPhotoPath;
            if (_cursor.isNull(_cursorIndexOfFoodPhotoPath)) {
              _tmpFoodPhotoPath = null;
            } else {
              _tmpFoodPhotoPath = _cursor.getString(_cursorIndexOfFoodPhotoPath);
            }
            _item = new BloodSugar(_tmpId,_tmpValue,_tmpMeasurementType,_tmpDateTime,_tmpNote,_tmpMeterPhotoPath,_tmpFoodPhotoPath);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAverageBloodSugar(final Date startDate, final Date endDate,
      final Continuation<? super Float> $completion) {
    final String _sql = "SELECT AVG(value) FROM blood_sugar WHERE dateTime BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @Nullable
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp_2;
            if (_cursor.isNull(0)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getFloat(0);
            }
            _result = _tmp_2;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMaxBloodSugar(final Date startDate, final Date endDate,
      final Continuation<? super Float> $completion) {
    final String _sql = "SELECT MAX(value) FROM blood_sugar WHERE dateTime BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @Nullable
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp_2;
            if (_cursor.isNull(0)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getFloat(0);
            }
            _result = _tmp_2;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMinBloodSugar(final Date startDate, final Date endDate,
      final Continuation<? super Float> $completion) {
    final String _sql = "SELECT MIN(value) FROM blood_sugar WHERE dateTime BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @Nullable
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp_2;
            if (_cursor.isNull(0)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getFloat(0);
            }
            _result = _tmp_2;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecordCount(final Date startDate, final Date endDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM blood_sugar WHERE dateTime BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp_2;
            if (_cursor.isNull(0)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(0);
            }
            _result = _tmp_2;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
