# 🚀 血糖记录APP快速启动指南

## 📋 前提条件

1. **安装Android Studio**
   - 下载最新版本的Android Studio
   - 确保安装了Android SDK和构建工具

2. **系统要求**
   - Windows 10/11, macOS 10.14+, 或 Linux
   - 至少8GB RAM (推荐16GB)
   - 至少4GB可用磁盘空间

## 🔧 在Android Studio中运行项目

### 步骤1: 打开项目
1. 启动Android Studio
2. 选择 "Open an Existing Project"
3. 导航到项目文件夹 `C:\Users\<USER>\AndroidStudioProjects\Myapp`
4. 点击 "OK"

### 步骤2: 等待同步
1. Android Studio会自动开始Gradle同步
2. 等待同步完成（可能需要几分钟）
3. 如果提示安装缺失的SDK组件，点击"Install"

### 步骤3: 配置设备
**选项A: 使用Android模拟器**
1. 点击工具栏的"AVD Manager"图标
2. 点击"Create Virtual Device"
3. 选择设备型号（推荐Pixel 6）
4. 选择系统镜像（推荐API 34或35）
5. 点击"Finish"创建模拟器

**选项B: 使用真实设备**
1. 在手机上启用开发者选项和USB调试
2. 用USB线连接手机到电脑
3. 在手机上允许USB调试

### 步骤4: 运行应用
1. 确保设备已连接或模拟器已启动
2. 点击工具栏的绿色"Run"按钮（▶️）
3. 选择目标设备
4. 等待应用安装和启动

## 🎯 预期结果

应用启动后，您应该看到:
1. **顶部**: 蓝色的"血糖记录"标题栏
2. **中间**: 统计卡片显示"最近30天统计"（初始为空）
3. **下方**: 血糖趋势图（显示"暂无数据"）
4. **底部**: "还没有血糖记录"的提示文字
5. **右下角**: 蓝色的"+"浮动按钮

## 🧪 测试功能

### 添加第一条记录:
1. 点击右下角的"+"按钮
2. 输入血糖值，例如: `6.5`
3. 选择测量类型，例如: "空腹"
4. 添加备注（可选）: "早晨测量"
5. 点击"添加"

### 查看记录:
- 新记录会出现在列表中
- 统计卡片会更新显示数据
- 图表会显示数据点

### 编辑记录:
1. 点击记录右侧的编辑图标（铅笔）
2. 修改数值或类型
3. 点击"保存"

### 删除记录:
1. 点击记录右侧的删除图标（垃圾桶）
2. 在确认对话框中点击"删除"

## ⚠️ 常见问题

### 问题1: Gradle同步失败
**解决方案**:
1. 检查网络连接
2. 点击 "File" → "Sync Project with Gradle Files"
3. 如果仍然失败，点击 "Build" → "Clean Project"

### 问题2: 应用无法启动
**解决方案**:
1. 检查设备是否正确连接
2. 确保模拟器有足够的内存
3. 查看Logcat中的错误信息

### 问题3: 编译错误
**解决方案**:
1. 确保使用最新版本的Android Studio
2. 检查是否安装了所需的SDK组件
3. 尝试 "Build" → "Rebuild Project"

## 📱 应用功能概览

### 主要特性:
- ✅ 添加血糖记录
- ✅ 编辑和删除记录
- ✅ 查看统计信息
- ✅ 血糖趋势图表
- ✅ 多种测量类型
- ✅ 数据本地存储

### 支持的测量类型:
- 空腹
- 早餐前/后
- 午餐前/后
- 晚餐前/后
- 睡前
- 随机

### 血糖参考值:
- 正常: 3.9-7.8 mmol/L (蓝色显示)
- 异常: <3.9 或 >7.8 mmol/L (红色显示)

## 🎉 成功运行标志

如果您能够:
1. ✅ 成功添加血糖记录
2. ✅ 看到统计数据更新
3. ✅ 在图表中看到数据点
4. ✅ 编辑和删除记录

恭喜！您的血糖记录APP已经成功运行！

## 📞 需要帮助?

如果遇到问题:
1. 查看 `TROUBLESHOOTING.md` 文件
2. 检查Android Studio的Build窗口中的错误信息
3. 确保按照本指南的步骤操作

祝您使用愉快！🎊
