package com.example.myapp.data.dao

import androidx.room.*
import com.example.myapp.data.entity.BloodSugar
import com.example.myapp.data.entity.MeasurementType
import kotlinx.coroutines.flow.Flow
import java.util.Date

@Dao
interface BloodSugarDao {
    
    @Query("SELECT * FROM blood_sugar ORDER BY dateTime DESC")
    fun getAllBloodSugars(): Flow<List<BloodSugar>>
    
    @Query("SELECT * FROM blood_sugar WHERE id = :id")
    suspend fun getBloodSugarById(id: Long): BloodSugar?
    
    @Query("SELECT * FROM blood_sugar WHERE dateTime BETWEEN :startDate AND :endDate ORDER BY dateTime DESC")
    fun getBloodSugarsByDateRange(startDate: Date, endDate: Date): Flow<List<BloodSugar>>
    
    @Query("SELECT * FROM blood_sugar WHERE measurementType = :type ORDER BY dateTime DESC")
    fun getBloodSugarsByType(type: MeasurementType): Flow<List<BloodSugar>>
    
    @Query("SELECT AVG(value) FROM blood_sugar WHERE dateTime BETWEEN :startDate AND :endDate")
    suspend fun getAverageBloodSugar(startDate: Date, endDate: Date): Float?
    
    @Query("SELECT MAX(value) FROM blood_sugar WHERE dateTime BETWEEN :startDate AND :endDate")
    suspend fun getMaxBloodSugar(startDate: Date, endDate: Date): Float?
    
    @Query("SELECT MIN(value) FROM blood_sugar WHERE dateTime BETWEEN :startDate AND :endDate")
    suspend fun getMinBloodSugar(startDate: Date, endDate: Date): Float?
    
    @Query("SELECT COUNT(*) FROM blood_sugar WHERE dateTime BETWEEN :startDate AND :endDate")
    suspend fun getRecordCount(startDate: Date, endDate: Date): Int
    
    @Insert
    suspend fun insertBloodSugar(bloodSugar: BloodSugar): Long
    
    @Update
    suspend fun updateBloodSugar(bloodSugar: BloodSugar)
    
    @Delete
    suspend fun deleteBloodSugar(bloodSugar: BloodSugar)
    
    @Query("DELETE FROM blood_sugar WHERE id = :id")
    suspend fun deleteBloodSugarById(id: Long)
}
