{"logs": [{"outputFile": "com.example.myapp-mergeDebugResources-57:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\74a2a50a3cf75b402410cb079508727a\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1003,1072,1161,1252,1324,1403,1473", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,998,1067,1156,1247,1319,1398,1468,1589"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "829,925,1011,1110,1213,1303,1383,7633,7723,7810,7881,7950,8039,8130,8303,8382,8452", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "920,1006,1105,1208,1298,1378,1474,7718,7805,7876,7945,8034,8125,8197,8377,8447,8568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1199c7ee149e3eccc471d61f7d603781\\transformed\\core-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,607,711,8202", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "196,298,397,496,602,706,824,8298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6c1b5f2840ec1b7ea00a29b86e3ab0de\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8573,8661", "endColumns": "87,90", "endOffsets": "8656,8747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3dbf5ba78cbeee9020b93a3c65c010c\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4648,4735,4848,4928,5013,5114,5217,5311,5413,5499,5605,5701,5809,5926,6006,6112", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4643,4730,4843,4923,5008,5109,5212,5306,5408,5494,5600,5696,5804,5921,6001,6107,6204"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1479,1597,1714,1835,1949,2049,2148,2264,2400,2518,2666,2752,2854,2948,3046,3168,3288,3395,3530,3667,3802,3974,4103,4220,4338,4459,4554,4651,4769,4908,5011,5113,5224,5362,5502,5613,5716,5793,5888,5986,6072,6159,6272,6352,6437,6538,6641,6735,6837,6923,7029,7125,7233,7350,7430,7536", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "1592,1709,1830,1944,2044,2143,2259,2395,2513,2661,2747,2849,2943,3041,3163,3283,3390,3525,3662,3797,3969,4098,4215,4333,4454,4549,4646,4764,4903,5006,5108,5219,5357,5497,5608,5711,5788,5883,5981,6067,6154,6267,6347,6432,6533,6636,6730,6832,6918,7024,7120,7228,7345,7425,7531,7628"}}]}]}