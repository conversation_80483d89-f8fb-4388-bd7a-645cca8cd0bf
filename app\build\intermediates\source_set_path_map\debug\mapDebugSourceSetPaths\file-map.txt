com.example.myapp-customview-poolingcontainer-1.0.0-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011867c50149d1ec2ed21fd84821cc96\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapp-ui-util-release-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03d79ec99873ae3e8d9de194fecaf3a6\transformed\ui-util-release\res
com.example.myapp-ui-graphics-release-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07c05dee3afd2b58224c80a52bd00e97\transformed\ui-graphics-release\res
com.example.myapp-core-1.12.0-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1199c7ee149e3eccc471d61f7d603781\transformed\core-1.12.0\res
com.example.myapp-lifecycle-viewmodel-compose-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1250eb1b523091e2a261c92c5e697254\transformed\lifecycle-viewmodel-compose-2.7.0\res
com.example.myapp-activity-compose-1.8.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16ae76f0e8727228524a54e9771d1380\transformed\activity-compose-1.8.0\res
com.example.myapp-startup-runtime-1.1.1-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\176d973377d132381e289966a6dacb40\transformed\startup-runtime-1.1.1\res
com.example.myapp-ui-tooling-preview-release-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\184327eecaaa893cc76db5d712864930\transformed\ui-tooling-preview-release\res
com.example.myapp-material-icons-core-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d85e28e822bfa8ceb2dd7ebdc5b06d7\transformed\material-icons-core-release\res
com.example.myapp-lifecycle-runtime-ktx-2.7.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21223ff6e7c8442afe67fb2c49f28b93\transformed\lifecycle-runtime-ktx-2.7.0\res
com.example.myapp-foundation-layout-release-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d068b8062986f40b34de0514f987a48\transformed\foundation-layout-release\res
com.example.myapp-savedstate-1.2.1-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b45d21ff40d515582a0e7cfaef0b23\transformed\savedstate-1.2.1\res
com.example.myapp-ui-text-release-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3aad40c68fa74efcd10eee63028d4f98\transformed\ui-text-release\res
com.example.myapp-runtime-saveable-release-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d560290b6308e81141a6691c3894324\transformed\runtime-saveable-release\res
com.example.myapp-navigation-compose-2.7.6-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424933c36d2e26ed38250f538673f8c1\transformed\navigation-compose-2.7.6\res
com.example.myapp-core-runtime-2.2.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\424eedabeba67a727cb7dbfb069ca9d7\transformed\core-runtime-2.2.0\res
com.example.myapp-material-release-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8795518d7e56ffcfaa6ec1a76faf96\transformed\material-release\res
com.example.myapp-ui-tooling-data-release-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f11ca454225844e2bfa82da4ef324de\transformed\ui-tooling-data-release\res
com.example.myapp-sqlite-framework-2.4.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f860fc3497cc38320668fba170a71f2\transformed\sqlite-framework-2.4.0\res
com.example.myapp-ui-tooling-release-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5045753aa3ba3093a60e09249be1ffbf\transformed\ui-tooling-release\res
com.example.myapp-lifecycle-viewmodel-savedstate-2.7.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5108bb87999bd159e4e31269e37bbf46\transformed\lifecycle-viewmodel-savedstate-2.7.0\res
com.example.myapp-lifecycle-process-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627ad22a550ee5e4e7f5da46dd55cda9\transformed\lifecycle-process-2.7.0\res
com.example.myapp-navigation-common-2.7.6-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65ec0caada01eda1656cd2f5ec32c2f7\transformed\navigation-common-2.7.6\res
com.example.myapp-foundation-release-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c1b5f2840ec1b7ea00a29b86e3ab0de\transformed\foundation-release\res
com.example.myapp-ui-release-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74a2a50a3cf75b402410cb079508727a\transformed\ui-release\res
com.example.myapp-ui-geometry-release-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c95ac6d2397757e8712319a2abc2de\transformed\ui-geometry-release\res
com.example.myapp-animation-core-release-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80bd386c4131a07dc1f8f2781b5b8acb\transformed\animation-core-release\res
com.example.myapp-navigation-runtime-2.7.6-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89994571a4cff3babcff62e2e04557da\transformed\navigation-runtime-2.7.6\res
com.example.myapp-hilt-navigation-1.1.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90895b5e60146dd6f96ce140e1343b7d\transformed\hilt-navigation-1.1.0\res
com.example.myapp-navigation-runtime-ktx-2.7.6-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93a6314ab5603eb1e43c14579eabfdca\transformed\navigation-runtime-ktx-2.7.6\res
com.example.myapp-emoji2-1.3.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\94063e132b860fa4320678fdc2ca5f5b\transformed\emoji2-1.3.0\res
com.example.myapp-savedstate-ktx-1.2.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970e2c165369744e5a870c0b0ca977cc\transformed\savedstate-ktx-1.2.1\res
com.example.myapp-material-ripple-release-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9454674f97a0861545825b12fc5e111\transformed\material-ripple-release\res
com.example.myapp-profileinstaller-1.3.0-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afbe339c655994d2189d310397f825c1\transformed\profileinstaller-1.3.0\res
com.example.myapp-ui-test-manifest-1.6.6-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4823dbe46cac0bbcfc4632a021dee20\transformed\ui-test-manifest-1.6.6\res
com.example.myapp-runtime-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b620556a561feb8acfd0a927321e9f08\transformed\runtime-release\res
com.example.myapp-animation-release-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1dfdb08c9a6696e67a4e86eb72b94fa\transformed\animation-release\res
com.example.myapp-material3-release-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3dbf5ba78cbeee9020b93a3c65c010c\transformed\material3-release\res
com.example.myapp-core-ktx-1.12.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7766c17cde28a1b317c180e850b0eca\transformed\core-ktx-1.12.0\res
com.example.myapp-activity-ktx-1.8.0-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c848bca3033706d71c98f1d0672e5c1f\transformed\activity-ktx-1.8.0\res
com.example.myapp-hilt-navigation-compose-1.1.0-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb7310445152b0bbc749280c1bfab79a\transformed\hilt-navigation-compose-1.1.0\res
com.example.myapp-activity-1.8.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdb73159a1e77936d4925b0cdd85e2e5\transformed\activity-1.8.0\res
com.example.myapp-lifecycle-livedata-2.7.0-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf870b4eb1b46f25308d48ddcaec23ea\transformed\lifecycle-livedata-2.7.0\res
com.example.myapp-lifecycle-livedata-core-ktx-2.7.0-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0c961c02c9be885a8d4b5c85275fdca\transformed\lifecycle-livedata-core-ktx-2.7.0\res
com.example.myapp-navigation-common-ktx-2.7.6-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1a1718fc1f1c2c749c750cdabfe0082\transformed\navigation-common-ktx-2.7.6\res
com.example.myapp-lifecycle-runtime-2.7.0-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4aeb05896e448478c5bc7d17583d5b7\transformed\lifecycle-runtime-2.7.0\res
com.example.myapp-ui-unit-release-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4f6745e6425fc2c87bb6ba378e6b54b\transformed\ui-unit-release\res
com.example.myapp-room-runtime-2.6.1-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d785e9a32b6bfe630ab54a5379309181\transformed\room-runtime-2.6.1\res
com.example.myapp-annotation-experimental-1.4.0-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d861f308de34c687690c2b12f7b18a54\transformed\annotation-experimental-1.4.0\res
com.example.myapp-lifecycle-viewmodel-ktx-2.7.0-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbff3b8e95acb6a3ff892a7f5756c339\transformed\lifecycle-viewmodel-ktx-2.7.0\res
com.example.myapp-fragment-1.5.1-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0721bb0b65759a90ae8900b0104e8f5\transformed\fragment-1.5.1\res
com.example.myapp-sqlite-2.4.0-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eba72644728de42fce092df05177ca5b\transformed\sqlite-2.4.0\res
com.example.myapp-lifecycle-livedata-core-2.7.0-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec6280272ba517380aa35f3faced0eb3\transformed\lifecycle-livedata-core-2.7.0\res
com.example.myapp-room-ktx-2.6.1-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8007a502693774256296a2c2211e68\transformed\room-ktx-2.6.1\res
com.example.myapp-lifecycle-viewmodel-2.7.0-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbddd76c518d9487838d67d809f42a09\transformed\lifecycle-viewmodel-2.7.0\res
com.example.myapp-pngs-55 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\build\generated\res\pngs\debug
com.example.myapp-resValues-56 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\build\generated\res\resValues\debug
com.example.myapp-packageDebugResources-57 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapp-packageDebugResources-58 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapp-debug-59 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapp-debug-60 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\debug\res
com.example.myapp-main-61 C:\Users\<USER>\AndroidStudioProjects\Myapp\app\src\main\res
