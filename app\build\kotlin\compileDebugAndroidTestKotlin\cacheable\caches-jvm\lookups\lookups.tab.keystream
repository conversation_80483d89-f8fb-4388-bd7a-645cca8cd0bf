  
targetContext android.app.Instrumentation  packageName android.content.Context  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  
AndroidJUnit4 com.example.myapp  ExampleInstrumentedTest com.example.myapp  InstrumentationRegistry com.example.myapp  RunWith com.example.myapp  Test com.example.myapp  assertEquals com.example.myapp  InstrumentationRegistry )com.example.myapp.ExampleInstrumentedTest  assertEquals )com.example.myapp.ExampleInstrumentedTest  
AndroidJUnit4 	org.junit  InstrumentationRegistry 	org.junit  RunWith 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         